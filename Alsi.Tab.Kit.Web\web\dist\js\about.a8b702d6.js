"use strict";(self["webpackChunktab_kit_web"]=self["webpackChunktab_kit_web"]||[]).push([[594],{2378:function(a,s,c){c.r(s),c.d(s,{default:function(){return m}});var e=c(6768),t=c(3935);const n={class:"about"},l={class:"app-info-cards"},i={class:"card-header"},o={class:"info-content"},d={class:"contact-section"},k={class:"contact-methods"},p={class:"contact-item"},v={class:"card-header"};function h(a,s,c,h,r,u){const L=(0,e.g2)("font-awesome-icon"),b=(0,e.g2)("el-card");return(0,e.uX)(),(0,e.CE)("div",n,[s[8]||(s[8]=(0,e.Fv)('<div class="page-header" data-v-8bf9f630><div class="app-logo-section" data-v-8bf9f630><img src="'+t+'" alt="TabKit Logo" class="logo-icon" data-v-8bf9f630><div class="app-info" data-v-8bf9f630><h1 data-v-8bf9f630>TabKit</h1><p class="version" data-v-8bf9f630>版本 1.0.7</p></div></div></div>',1)),(0,e.Lk)("div",l,[(0,e.bF)(b,{class:"info-card"},{header:(0,e.k6)(()=>[(0,e.Lk)("div",i,[(0,e.bF)(L,{icon:"info-circle"}),s[1]||(s[1]=(0,e.Lk)("span",null,"关于 TabKit",-1))])]),default:(0,e.k6)(()=>[(0,e.Lk)("div",o,[s[5]||(s[5]=(0,e.Lk)("p",{class:"description"}," Alsi.Tab.Kit 是一个集成多种工具的桌面应用程序，旨在提高工作效率和简化日常任务。 ",-1)),(0,e.Lk)("div",d,[s[4]||(s[4]=(0,e.Lk)("span",null,"使用过程中遇到问题或有任何建议，欢迎提出：",-1)),(0,e.Lk)("div",k,[(0,e.Lk)("div",{class:"contact-item",onClick:s[0]||(s[0]=(...s)=>a.openTeams&&a.openTeams(...s))},[(0,e.bF)(L,{icon:"comment-alt",class:"contact-icon"}),s[2]||(s[2]=(0,e.Lk)("span",{class:"contact-link"},"Feedback via Teams",-1))]),(0,e.Lk)("div",p,[(0,e.bF)(L,{icon:"envelope",class:"contact-icon"}),s[3]||(s[3]=(0,e.Lk)("a",{href:"mailto:<EMAIL>",class:"contact-link"},"<EMAIL>",-1))])])])])]),_:1}),(0,e.bF)(b,{class:"info-card"},{header:(0,e.k6)(()=>[(0,e.Lk)("div",v,[(0,e.bF)(L,{icon:"code"}),s[6]||(s[6]=(0,e.Lk)("span",null,"技术信息",-1))])]),default:(0,e.k6)(()=>[s[7]||(s[7]=(0,e.Lk)("div",{class:"tech-info"},[(0,e.Lk)("div",{class:"tech-grid"},[(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"前端框架:"),(0,e.Lk)("span",{class:"tech-value"},"Vue 3 + TypeScript")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"UI组件库:"),(0,e.Lk)("span",{class:"tech-value"},"Element Plus")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"图标库:"),(0,e.Lk)("span",{class:"tech-value"},"FontAwesome")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"后端框架:"),(0,e.Lk)("span",{class:"tech-value"},".NET Framework")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"桌面框架:"),(0,e.Lk)("span",{class:"tech-value"},"WPF + WebView2")]),(0,e.Lk)("div",{class:"tech-item"},[(0,e.Lk)("span",{class:"tech-label"},"构建工具:"),(0,e.Lk)("span",{class:"tech-value"},"Vue CLI + Webpack")])])],-1))]),_:1,__:[7]})])])}var r=c(292),u=c(1021),L=(0,e.pM)({name:"AboutView",components:{FontAwesomeIcon:r.gc},setup(){const a=async()=>{const a="https://teams.microsoft.com/l/chat/0/0?users=<EMAIL>";await u.GQ.explorer.startProcess(a)};return{openTeams:a}}}),b=c(1241);const f=(0,b.A)(L,[["render",h],["__scopeId","data-v-8bf9f630"]]);var m=f}}]);
//# sourceMappingURL=about.a8b702d6.js.map