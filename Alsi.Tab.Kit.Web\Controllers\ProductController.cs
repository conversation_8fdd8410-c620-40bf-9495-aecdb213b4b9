using Alsi.App.Desktop.Utils;
using Alsi.Tab.Kit.Core.Services.Product;
using Alsi.Tab.Kit.Web.Dto;
using System;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Tab.Kit.Web.Controllers
{
    public class ProductController : WebControllerBase
    {
        private readonly ProductService _productService;

        public ProductController()
        {
            _productService = new ProductService();
        }

        [HttpGet]
        [ActionName("list")]
        public async Task<IHttpActionResult> GetProducts(string baseUrl)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(baseUrl))
                {
                    return BadRequest("baseUrl 参数不能为空");
                }

                var products = await _productService.GetProductsAsync(baseUrl);
                return Ok(products);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpGet]
        [ActionName("versions")]
        public async Task<IHttpActionResult> GetProductVersions(string baseUrl, Guid productId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(baseUrl))
                {
                    return BadRequest("baseUrl 参数不能为空");
                }

                if (productId == Guid.Empty)
                {
                    return BadRequest("productId 参数不能为空");
                }

                var versions = await _productService.GetProductVersionsAsync(baseUrl, productId);
                return Ok(versions);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpPost]
        [ActionName("select-zip-file")]
        public IHttpActionResult SelectZipFile()
        {
            UiUtils.SelectFile(out var filePath, "ZIP File", "*.zip");
            return Ok(filePath);
        }

        [HttpPost]
        [ActionName("publish-version")]
        public async Task<IHttpActionResult> PublishProductVersion([FromBody] ProductVersionZipRequestDto request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest("请求参数不能为空");
                }

                var serviceRequest = _mapper.Map<ProductVersionZipRequestDto, ProductVersionZipRequest>(request);
                serviceRequest.Validate();

                await _productService.PublishProductVersionZipAsync(serviceRequest);
                return Ok(new { Success = true, Message = "产品版本发布成功" });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }
    }
}
