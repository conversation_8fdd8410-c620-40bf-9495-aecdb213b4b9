﻿using Alsi.Tab.Kit.Core.Services.Capl.Params;
using System;
using System.Linq;

namespace Alsi.Tab.Kit.Core.Services.Capl.Cin
{
    public static class CinTypes
    {
        // 枚举定义
        public enum VEHICLE_CARMODE
        {
            CARMODE_NORMAL = 0,
            CARMODE_TRANSPORT = 1,
            CARMODE_FACTORY = 2,
            CARMODE_CRASH = 3,
            CARMODE_DYNO = 5
        }

        public enum VEHICLE_USAGEMODE
        {
            USAGEMODE_ABANDONED = 0,
            USAGEMODE_INACTIVE = 1,
            USAGEMODE_CONVENIENCE = 2,
            USAGEMODE_ACTIVE = 0xB,
            USAGEMODE_DRIVING = 0xD
        }

        public enum BUS_TYPE
        {
            CAN_BUS = 0,
            CANFD_BUS = 1,
            LIN_BUS = 2,
            ETH_BUS = 3,
            FLEXRAY_BUS = 4
        }

        // 共通常数，不需配置
        public class StruSIDandSUBID
        {
            public byte sessionMask;     // 支持的服务的session掩码
            [CinProp(isHex: true)]
            public byte sid;             // 支持的服务ID（ServiceID）
            public byte subIDcnt;        // 支持的子服务个数
            public byte[] subID;         // 支持的所有子服务 [20]

            public StruSIDandSUBID()
            {
                sessionMask = 0;
                sid = 0;
                subIDcnt = 0;
                subID = Array.Empty<byte>();
            }
        }

        public class StruDIDinLevel
        {
            public byte sessionMask;

            [CinProp(isHex: true)]
            public uint did;             // dword -> uint

            [CinProp(isHex: true)]
            public byte saLevel;

            public int valueLength;

            public byte[] value;         // [200]

            public StruDIDinLevel()
            {
                sessionMask = 0;
                did = 0;
                saLevel = 0;
                valueLength = 0;
                value = Array.Empty<byte>();
            }

            internal static StruDIDinLevel FromCaplParam(STRU_DID struDid)
            {
                return new StruDIDinLevel
                {
                    sessionMask = struDid.sessionMask,
                    did = struDid.did,
                    saLevel = struDid.saLevel,
                    valueLength = struDid.valueLength,
                    value = struDid.value
                };
            }
        }

        public class StruRoutine
        {
            [CinProp(isHex: true)]
            public byte subId;           // 对应RID的subID

            public uint reqLength;       // 对应RID的请求长度 (dword -> uint)

            public uint resLength;       // 对应RID的回复长度 (dword -> uint)

            public byte[] reqBytes;      // 对应RID的请求消息 [2000]

            public StruRoutine()
            {
                subId = 0;
                reqLength = 0;
                resLength = 0;
                reqBytes = Array.Empty<byte>();
            }
        }

        public class StruRID
        {
            public byte sessionMask;                       // 支持的RID的session掩码

            [CinProp(isHex: true)]
            public uint rid;                               // 支持的RID (dword -> uint)
            public byte routineType;                       // 支持的routineType,定义见Diag Service SWTD
            public byte[] securityLevels;                  // 所需的安全等级 [10]
            public StruRoutine[] struRoutineList;          // 31服务Parameter的数据结构体 [4]

            public StruRID()
            {
                sessionMask = 0;
                rid = 0;
                routineType = 0;
                securityLevels = Array.Empty<byte>();
                struRoutineList = Array.Empty<StruRoutine>();
            }
        }

        // 仿真信号结构体
        public class StruE2Esig
        {
            public int startBit;      // word -> int

            public byte length;

            [CinProp(isHex: true)]
            public ulong value;          // qword -> ulong

            public StruE2Esig()
            {
                startBit = 0;
                length = 0;
                value = 0;
            }

            internal static StruE2Esig FromCaplParam(E2ESignal caplStruE2Esig)
            {
                return new StruE2Esig
                {
                    startBit = (int)caplStruE2Esig.startBit,
                    length = caplStruE2Esig.length,
                };
            }
        }

        public class StruE2EsigGroup
        {
            [CinProp(isHex: true)]
            public int dataId;        // word -> int

            public int updateBit;     // UB bit,0xFFFF means ignore. (word -> int)
            public int chksStartBit;  // Checksum start bit and the size is always 8 (word -> int)
            public int cntrStartBit;  // AliveCnt start bit and the size is always 4 (word -> int)
            public StruE2Esig[] struE2EsigList; // [50]

            public StruE2EsigGroup()
            {
                dataId = 0;
                updateBit = 0;
                chksStartBit = 0;
                cntrStartBit = 0;
                struE2EsigList = Array.Empty<StruE2Esig>();
            }

            internal static StruE2EsigGroup FromCaplParam(E2ESignalGroup group)
            {
                var result = new StruE2EsigGroup
                {
                    dataId = (int)group.dataId,
                };

                var ubSignal = group.signals.FirstOrDefault(x => x.sigType == SigType.UB);
                if (ubSignal != null)
                {
                    result.updateBit = (int)ubSignal.startBit;
                }

                var chkSignal = group.signals.FirstOrDefault(x => x.name.IsChks());
                if (chkSignal != null)
                {
                    result.chksStartBit = (int)chkSignal.startBit;
                }

                var cntrSignal = group.signals.FirstOrDefault(x => x.name.IsCntr());
                if (cntrSignal != null)
                {
                    result.cntrStartBit = (int)cntrSignal.startBit;
                }

                result.struE2EsigList = group.signals.Select(StruE2Esig.FromCaplParam).ToArray();
                return result;
            }
        }

        public class StruE2EsigGroupFlexRay
        {
            [CinProp(isHex: true)]
            public int dataId;        // word -> int

            [CinProp(isHex: true)]
            public uint dtc;             // dword -> uint

            public int updateBit;     // UB bit,0xFFFF means ignore. (word -> int)
            public int chksStartBit;  // Checksum start bit and the size is always 8 (word -> int)
            public int cntrStartBit;  // AliveCnt start bit and the size is always 4 (word -> int)
            public StruE2Esig[] struE2EsigList; // [50]

            public StruE2EsigGroupFlexRay()
            {
                dataId = 0;
                dtc = 0;
                updateBit = 0;
                chksStartBit = 0;
                cntrStartBit = 0;
                struE2EsigList = Array.Empty<StruE2Esig>();
            }
        }

        public class StruE2Emsg
        {
            public byte busType;
            public byte msgChn;

            [CinProp(isHex: true)]
            public uint msgID;           // dword -> uint

            public uint cycle;           // dword -> uint

            public byte dataLength;

            public byte byteOrder;       // 0 means Moto, 1 means Intel
            public StruE2EsigGroup[] struE2EsigGroupList; // [20]

            public StruE2Emsg()
            {
                busType = 0;
                msgChn = 0;
                msgID = 0;
                cycle = 0;
                dataLength = 0;
                byteOrder = 0;
                struE2EsigGroupList = Array.Empty<StruE2EsigGroup>();
            }

            internal static StruE2Emsg FromCaplParam(E2EFrame frame)
            {
                var result = new StruE2Emsg
                {
                    msgID = frame.frameID,
                    cycle = (uint)frame.cycle,
                    dataLength = (byte)frame.dataLength,
                    byteOrder = (byte)frame.byteOrder,
                    struE2EsigGroupList = frame.signalGroups.Select(StruE2EsigGroup.FromCaplParam).ToArray()
                };
                return result;
            }
        }
        public class StruVehicleModeMsg
        {
            public byte busType;
            public byte msgChn;

            [CinProp(isHex: true)]
            public uint msgID;           // dword -> uint

            public uint cycle;           // dword -> uint
            public byte dataLength;
            public byte byteOrder;
            public int carModeBit;    // word -> int
            public int usageModeBit;  // word -> int
            public int powerLvlBit;   // word -> int
            public StruE2EsigGroup struE2EsigGroup;

            public StruVehicleModeMsg()
            {
                busType = 0;
                msgChn = 0;
                msgID = 0;
                cycle = 0;
                dataLength = 0;
                byteOrder = 0;
                carModeBit = 0;
                usageModeBit = 0;
                powerLvlBit = 0;
                struE2EsigGroup = new StruE2EsigGroup();
            }

            internal static StruVehicleModeMsg FromCaplParam(E2EFrame frame)
            {
                var result = new StruVehicleModeMsg
                {
                    msgID = frame.frameID,
                    cycle = (uint)frame.cycle,
                    dataLength = (byte)frame.dataLength,
                    byteOrder = (byte)frame.byteOrder,
                };

                var carModeSigGroup = frame.signalGroups.FirstOrDefault(x => x.signals.Any(signal => signal.sigType == SigType.CarMode));
                if (carModeSigGroup != null)
                {
                    result.struE2EsigGroup = StruE2EsigGroup.FromCaplParam(carModeSigGroup);
                }
                return result;
            }
        }

        // FlexRay新增E2E参数结构体
        public class SlotFormat
        {
            [CinProp(isHex: true)]
            public byte slotID;
            public byte baseCycle;
            public byte cycleRepetition;

            public SlotFormat()
            {
                slotID = 0;
                baseCycle = 0;
                cycleRepetition = 0;
            }
        }

        public class StruVehicleModeMsgOnFlexRay
        {
            public SlotFormat slotFormat;
            public byte dataLength;
            public byte byteOrder;
            public int carModeBit;    // word -> int
            public int usageModeBit;  // word -> int
            public int powerLvlBit;   // word -> int
            public StruE2EsigGroup struE2EsigGroup;

            public StruVehicleModeMsgOnFlexRay()
            {
                slotFormat = new SlotFormat();
                dataLength = 0;
                byteOrder = 0;
                carModeBit = 0;
                usageModeBit = 0;
                powerLvlBit = 0;
                struE2EsigGroup = new StruE2EsigGroup();
            }
        }

        public class StruE2EmsgFlexRay
        {
            public SlotFormat slotFormat;
            public byte dataLength;
            public byte byteOrder;       // 0 means Moto, 1 means Intel
            public StruE2EsigGroupFlexRay[] struE2EsigGroupList; // [20]

            public StruE2EmsgFlexRay()
            {
                slotFormat = new SlotFormat();
                dataLength = 0;
                byteOrder = 0;
                struE2EsigGroupList = Array.Empty<StruE2EsigGroupFlexRay>();
            }
        }

        public class StruE2EdtcInfo
        {
            [CinProp(isHex: true)]
            public uint dtc;             // dword -> uint

            [CinProp(isHex: true)]
            public uint msgId;           // dword -> uint

            public uint testPeriod;      // dword -> uint
            public int maxValue;      // word -> int
            public int stepUp;        // word -> int
            public int stepDown;      // word -> int
            public int confirmedDTCLimit; // word -> int
            public int agedDTCLimit;  // word -> int
            public int testFailedLimit; // word -> int

            public StruE2EdtcInfo()
            {
                dtc = 0;
                msgId = 0;
                testPeriod = 0;
                maxValue = 0;
                stepUp = 0;
                stepDown = 0;
                confirmedDTCLimit = 0;
                agedDTCLimit = 0;
                testFailedLimit = 0;
            }

            internal static StruE2EdtcInfo FromCaplParam(DTCInformation dTCInformation)
            {
                return new StruE2EdtcInfo
                {
                    dtc = dTCInformation.dtc,
                    msgId = dTCInformation.msgId,
                    testPeriod = (uint)dTCInformation.testPeriod,
                    maxValue = dTCInformation.maxValue,
                    stepUp = dTCInformation.stepUp,
                    stepDown = dTCInformation.stepDown,
                    confirmedDTCLimit = dTCInformation.confirmedDTCLimit,
                    agedDTCLimit = dTCInformation.agedDTCLimit,
                    testFailedLimit = dTCInformation.testFailedLimit
                };
            }
        }
    }
}
