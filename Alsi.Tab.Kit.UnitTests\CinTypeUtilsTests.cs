using Alsi.Tab.Kit.Core.Services.Capl.Cin;
using static Alsi.Tab.Kit.Core.Services.Capl.Cin.CinTypes;

namespace Alsi.Tab.Kit.UnitTests;

public class CinTypeUtilsTests
{
    #region 简单类型测试

    [Fact]
    public void ParseSimpleTypes_ShouldWork()
    {
        // 测试整数类型
        CinTypeUtils.ParseCinValue<int>("123").ShouldBe(123);
        CinTypeUtils.ParseCinValue<int>("0xFF").ShouldBe(255);
        CinTypeUtils.ParseCinValue<byte>("255").ShouldBe((byte)255);
        CinTypeUtils.ParseCinValue<long>("0x1000").ShouldBe(4096L);

        // 测试浮点类型
        CinTypeUtils.ParseCinValue<float>("3.14").ShouldBe(3.14f);
        CinTypeUtils.ParseCinValue<double>("2.718").ShouldBe(2.718);

        // 测试字符串类型
        CinTypeUtils.ParseCinValue<string>("hello").ShouldBe("hello");
        CinTypeUtils.ParseCinValue<string>("\"quoted\"").ShouldBe("quoted");

        // 测试布尔类型
        CinTypeUtils.ParseCinValue<bool>("true").ShouldBe(true);
        CinTypeUtils.ParseCinValue<bool>("false").ShouldBe(false);
        CinTypeUtils.ParseCinValue<bool>("1").ShouldBe(true);
    }

    [Fact]
    public void FormatSimpleTypes_ShouldWork()
    {
        // 测试整数格式化
        CinTypeUtils.ToCinValue(123).ShouldBe("123");
        CinTypeUtils.ToCinValue(255, isHex: true).ShouldBe("0xFF");

        // 测试浮点格式化
        CinTypeUtils.ToCinValue(3.14f).ShouldBe("3.14");
        CinTypeUtils.ToCinValue(2.718).ShouldBe("2.718");

        // 测试字符串格式化
        CinTypeUtils.ToCinValue("hello").ShouldBe("\"hello\"");

        // 测试布尔格式化
        CinTypeUtils.ToCinValue(true).ShouldBe("true");
        CinTypeUtils.ToCinValue(false).ShouldBe("false");
    }

    #endregion

    #region 数组类型测试

    [Fact]
    public void ParseArrayTypes_ShouldWork()
    {
        // 测试整数数组
        var intArray = CinTypeUtils.ParseCinValue<int[]>("{1, 2, 3, 4}");
        intArray.ShouldBe(new int[] { 1, 2, 3, 4 });

        // 测试十六进制数组
        var hexArray = CinTypeUtils.ParseCinValue<byte[]>("{0x10, 0x20, 0x30}");
        hexArray.ShouldBe(new byte[] { 0x10, 0x20, 0x30 });

        // 测试字符串数组
        var stringArray = CinTypeUtils.ParseCinValue<string[]>("{\"hello\", \"world\"}");
        stringArray.ShouldBe(new string[] { "hello", "world" });
    }

    [Fact]
    public void FormatArrayTypes_ShouldWork()
    {
        // 测试整数数组格式化
        var intArray = new int[] { 1, 2, 3, 4 };
        CinTypeUtils.ToCinValue(intArray).ShouldBe("{1, 2, 3, 4}");

        // 测试字节数组格式化
        var byteArray = new byte[] { 0x10, 0x20, 0x30 };
        CinTypeUtils.ToCinValue(byteArray).ShouldBe("{16, 32, 48}");

        // 测试字符串数组格式化
        var stringArray = new string[] { "hello", "world" };
        CinTypeUtils.ToCinValue(stringArray).ShouldBe("{\"hello\", \"world\"}");
    }

    #endregion

    #region 结构体类型测试

    [Fact]
    public void ParseStructTypes_ShouldWork()
    {
        // 测试STRU_DID结构体解析
        var struDid = CinTypeUtils.ParseCinValue<StruDIDinLevel>("{5, 0x9007, 5}");
        struDid.sessionMask.ShouldBe((byte)5);
        struDid.did.ShouldBe(0x9007u);
        struDid.saLevel.ShouldBe((byte)5);
    }

    [Fact]
    public void FormatStructTypes_ShouldWork()
    {
        // 测试STRU_DID结构体格式化
        var struDid = new StruDIDinLevel
        {
            sessionMask = 5,
            did = 0x9007,
            valueLength = 5
        };

        var result = CinTypeUtils.ToCinValue(struDid);
        result.ShouldBe("{5, 0x9007, 0x0, 5, {}}");
    }

    #endregion

    #region 结构体数组测试

    [Fact]
    public void ParseStructArrayTypes_ShouldWork()
    {
        // 测试结构体数组解析
        var structArray = CinTypeUtils.ParseCinValue<StruDIDinLevel[]>("{{4, 0x1001}, {8, 0x1002}}");
        structArray.Length.ShouldBe(2);
        structArray[0].did.ShouldBe(0x1001u);
        structArray[0].sessionMask.ShouldBe((byte)4);
        structArray[1].did.ShouldBe(0x1002u);
        structArray[1].sessionMask.ShouldBe((byte)8);
    }

    [Fact]
    public void FormatStructArrayTypes_ShouldWork()
    {
        // 测试结构体数组格式化
        var structArray = new StruDIDinLevel[]
        {
            new StruDIDinLevel { did = 0x1001, sessionMask = 4 },
            new StruDIDinLevel { did = 0x1002, sessionMask = 8 }
        };

        var result = CinTypeUtils.ToCinValue(structArray);
        result.ShouldBe("{{4, 0x1001, 0x0, 0, {}}, {8, 0x1002, 0x0, 0, {}}}");
    }

    #endregion

    #region 嵌套结构体测试

    [Fact]
    public void ParseNestedStructTypes_ShouldWork()
    {
        // 测试包含数组的结构体
        var snapshot = CinTypeUtils.ParseCinValue<StruRID>("{1, 2, 3, {4}, {{4,0x1001}, {8,0x1002}}}");
        snapshot.sessionMask.ShouldBe((byte)1);
        snapshot.rid.ShouldBe((byte)2);
        snapshot.routineType.ShouldBe((byte)3);
        snapshot.securityLevels[0].ShouldBe((byte)0x4);
        snapshot.struRoutineList.Length.ShouldBe(2);
        snapshot.struRoutineList[0].subId.ShouldBe((byte)4);
        snapshot.struRoutineList[0].reqLength.ShouldBe(0x1001u);
        snapshot.struRoutineList[1].subId.ShouldBe((byte)8);
        snapshot.struRoutineList[1].reqLength.ShouldBe(0x1002u);
    }

    [Fact]
    public void FormatNestedStructTypes_ShouldWork()
    {
        // 测试包含数组的结构体格式化
        var snapshot = new StruRID
        {
            sessionMask = 1,
            rid = 0x2,
            routineType = 3,
            securityLevels = new byte[] { 4 },
            struRoutineList = new StruRoutine[]
            {
                new StruRoutine { reqLength = 100, subId = 4 },
                new StruRoutine {reqLength = 200, subId = 8}
            }
        };

        var result = CinTypeUtils.ToCinValue(snapshot);
        result.ShouldBe("{1, 0x2, 3, {4}, {{0x4, 100, 0, {}}, {0x8, 200, 0, {}}}}");
    }

    #endregion

    #region 边界情况测试

    [Fact]
    public void ParseEmptyValues_ShouldWork()
    {
        // 测试空数组
        var emptyArray = CinTypeUtils.ParseCinValue<int[]>("{}");
        emptyArray.Length.ShouldBe(0);

        // 测试空字符串
        var emptyString = CinTypeUtils.ParseCinValue<string>("");
        emptyString.ShouldBe("");
    }

    [Fact]
    public void ParseInvalidValues_ShouldThrow()
    {
        // 测试无效的数字格式
        Should.Throw<Exception>(() => CinTypeUtils.ParseCinValue<int>("invalid"));

        // 测试无效的十六进制格式
        Should.Throw<Exception>(() => CinTypeUtils.ParseCinValue<int>("0xGGG"));

        // 测试无效的浮点格式
        Should.Throw<Exception>(() => CinTypeUtils.ParseCinValue<float>("not_a_float"));
    }

    [Fact]
    public void ParseWithWhitespace_ShouldWork()
    {
        // 测试包含空白字符的解析
        var result = CinTypeUtils.ParseCinValue<int[]>("{ 1 , 2 , 3 }");
        result.ShouldBe(new int[] { 1, 2, 3 });

        var structResult = CinTypeUtils.ParseCinValue<StruDIDinLevel>("{ 5 , 0x9007 , 5 }");
        structResult.sessionMask.ShouldBe((byte)5);
        structResult.did.ShouldBe(0x9007u);
        structResult.saLevel.ShouldBe((byte)5);
    }

    #endregion

    #region 兼容性测试

    [Fact]
    public void BackwardCompatibility_ShouldWork()
    {
        // 确保原有的测试仍然通过
        var struDid = CinTypeUtils.ParseCinValue<StruDIDinLevel>("{5,0x9007, 5}");
        ((int)struDid.sessionMask).ShouldBe(5);
        ((int)struDid.did).ShouldBe(0x9007);
        struDid.saLevel.ShouldBe((byte)5);
    }

    [Fact]
    public void RoundTripSerialization_ShouldWork()
    {
        // 测试序列化和反序列化的往返
        var original = new StruDIDinLevel
        {
            sessionMask = 10,
            did = 0x1234,
            valueLength = 20
        };

        var serialized = CinTypeUtils.ToCinValue(original);
        var deserialized = CinTypeUtils.ParseCinValue<StruDIDinLevel>(serialized);

        deserialized.sessionMask.ShouldBe(original.sessionMask);
        deserialized.did.ShouldBe(original.did);
        deserialized.valueLength.ShouldBe(original.valueLength);
    }

    #endregion

    #region 枚举类型测试

    // 测试用枚举类型
    public enum SigType
    {
        CarMode = 1,
        UsageMode = 2,
        ElPowerLevel = 3,
        UB = 4
    }

    // 测试用结构体，包含枚举字段
    public struct E2EFrameElement
    {
        public int offset;
        public int length;
        public SigType sigType;
    }

    public struct FrameFormat
    {
        [CinProp(isHex: true)]
        public int frameID;
        public int cycle;
        public byte dataLen;

        public FrameFormat(int frameID, byte cycle, byte dataLen)
        {
            this.frameID = frameID;
            this.cycle = cycle;
            this.dataLen = dataLen;
        }
    }

    public struct E2EFrame
    {
        public FrameFormat frameFormat;
        public int dlc;
        public E2EFrameElement[] elements;
    }

    [Fact]
    public void ParseEnum_ShouldWork()
    {
        // 测试按名称解析枚举
        CinTypeUtils.ParseCinValue<SigType>("CarMode").ShouldBe(SigType.CarMode);
        CinTypeUtils.ParseCinValue<SigType>("UsageMode").ShouldBe(SigType.UsageMode);
        CinTypeUtils.ParseCinValue<SigType>("ElPowerLevel").ShouldBe(SigType.ElPowerLevel);
        CinTypeUtils.ParseCinValue<SigType>("UB").ShouldBe(SigType.UB);

        // 测试按数值解析枚举
        CinTypeUtils.ParseCinValue<SigType>("1").ShouldBe(SigType.CarMode);
        CinTypeUtils.ParseCinValue<SigType>("2").ShouldBe(SigType.UsageMode);
        CinTypeUtils.ParseCinValue<SigType>("3").ShouldBe(SigType.ElPowerLevel);
        CinTypeUtils.ParseCinValue<SigType>("4").ShouldBe(SigType.UB);

        // 测试大小写不敏感
        CinTypeUtils.ParseCinValue<SigType>("carmode").ShouldBe(SigType.CarMode);
        CinTypeUtils.ParseCinValue<SigType>("USAGEMODE").ShouldBe(SigType.UsageMode);
    }

    [Fact]
    public void FormatEnum_ShouldWork()
    {
        // 测试枚举格式化
        CinTypeUtils.ToCinValue(SigType.CarMode).ShouldBe("CarMode");
        CinTypeUtils.ToCinValue(SigType.UsageMode).ShouldBe("UsageMode");
        CinTypeUtils.ToCinValue(SigType.ElPowerLevel).ShouldBe("ElPowerLevel");
        CinTypeUtils.ToCinValue(SigType.UB).ShouldBe("UB");
    }

    [Fact]
    public void ParseStructWithEnum_ShouldWork()
    {
        // 测试包含枚举的结构体解析
        var cinValue = "{12, 4, UsageMode}";
        var result = CinTypeUtils.ParseCinValue<E2EFrameElement>(cinValue);

        result.offset.ShouldBe(12);
        result.length.ShouldBe(4);
        result.sigType.ShouldBe(SigType.UsageMode);
    }

    [Fact]
    public void FormatStructWithEnum_ShouldWork()
    {
        // 测试包含枚举的结构体格式化
        var element = new E2EFrameElement
        {
            offset = 24,
            length = 4,
            sigType = SigType.UsageMode
        };
        var result = CinTypeUtils.ToCinValue(element);

        result.ShouldBe("{24, 4, UsageMode}");
    }

    [Fact]
    public void ParseComplexStructWithEnumArray_ShouldWork()
    {
        // 测试复杂结构体，包含枚举数组
        var cinValue = "{{0x120, 65}, 116, {{12, 4}, {8, 4}, {20, 4}, {16, 4}, {28, 4}, {24, 4, UsageMode}, {37, 3, CarMode}, {34, 4}, {33, 1}, {0, 8}, {32, 1, UB}}}";
        var result = CinTypeUtils.ParseCinValue<E2EFrame>(cinValue);

        result.frameFormat.frameID.ShouldBe(0x120);
        result.frameFormat.cycle.ShouldBe(65);
        result.dlc.ShouldBe(116);
        result.elements.Length.ShouldBe(11);

        // 验证包含枚举的元素
        result.elements[5].offset.ShouldBe(24);
        result.elements[5].length.ShouldBe(4);
        result.elements[5].sigType.ShouldBe(SigType.UsageMode);

        result.elements[6].offset.ShouldBe(37);
        result.elements[6].length.ShouldBe(3);
        result.elements[6].sigType.ShouldBe(SigType.CarMode);

        result.elements[10].offset.ShouldBe(32);
        result.elements[10].length.ShouldBe(1);
        result.elements[10].sigType.ShouldBe(SigType.UB);
    }

    [Fact]
    public void FormatComplexStructWithEnumArray_ShouldWork()
    {
        // 测试复杂结构体格式化
        var frame = new E2EFrame
        {
            frameFormat = new FrameFormat(0x120, 65, 0),
            dlc = 116,
            elements = new E2EFrameElement[]
            {
                new E2EFrameElement { offset = 12, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 8, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 20, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 16, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 28, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 24, length = 4, sigType = SigType.UsageMode },
                new E2EFrameElement { offset = 37, length = 3, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 34, length = 4, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 33, length = 1, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 0, length = 8, sigType = SigType.CarMode },
                new E2EFrameElement { offset = 32, length = 1, sigType = SigType.UB }
            }
        };

        var result = CinTypeUtils.ToCinValue(frame);

        // 验证结果包含正确的枚举名称
        result.ShouldContain("UsageMode");
        result.ShouldContain("CarMode");
        result.ShouldContain("UB");

        // 验证整体结构
        result.ShouldStartWith("{{0x120, 65, 0}, 116, {");
        result.ShouldEndWith("}}");
    }

    #endregion

    #region 字段和属性混合测试

    // 测试用类，包含字段和属性的混合
    public class MixedFieldsAndProperties
    {
        public int field1;
        public string Property1 { get; set; } = "";
        public byte field2;

        [CinProp(isHex: true)]
        public int Property2 { get; set; }

        public bool field3;
    }

    // 测试用结构体，包含字段和属性的混合
    public struct MixedStruct
    {
        public int field1;
        public string Property1 { get; set; }
        public byte field2;

        [CinProp(isHex: true)]
        public int Property2 { get; set; }
    }

    [Fact]
    public void FormatMixedFieldsAndProperties_ShouldWork()
    {
        // 测试包含字段和属性的类
        var mixedClass = new MixedFieldsAndProperties
        {
            field1 = 10,
            Property1 = "test",
            field2 = 20,
            Property2 = 0xFF,
            field3 = true
        };

        var result = CinTypeUtils.ToCinValue(mixedClass);

        // 验证结果包含所有字段和属性的值
        result.ShouldContain("10");
        result.ShouldContain("\"test\"");
        result.ShouldContain("20");
        result.ShouldContain("0xFF"); // Property2 应该使用十六进制格式
        result.ShouldContain("true");
    }

    [Fact]
    public void FormatMixedStruct_ShouldWork()
    {
        // 测试包含字段和属性的结构体
        var mixedStruct = new MixedStruct
        {
            field1 = 100,
            Property1 = "hello",
            field2 = 200,
            Property2 = 0x1234
        };

        var result = CinTypeUtils.ToCinValue(mixedStruct);

        // 验证结果包含所有字段和属性的值
        result.ShouldContain("100");
        result.ShouldContain("\"hello\"");
        result.ShouldContain("200");
        result.ShouldContain("0x1234"); // Property2 应该使用十六进制格式
    }

    [Fact]
    public void FormatMixedFieldsAndProperties_OrderShouldBePreserved()
    {
        // 测试字段和属性的顺序是否按照定义顺序保持
        var mixedClass = new MixedFieldsAndProperties
        {
            field1 = 1,
            Property1 = "a",
            field2 = 2,
            Property2 = 3,
            field3 = false
        };

        var result = CinTypeUtils.ToCinValue(mixedClass);

        // 验证顺序：field1, Property1, field2, Property2, field3
        // 由于MetadataToken的顺序可能因编译器而异，我们主要验证所有值都存在
        result.ShouldContain("1");
        result.ShouldContain("\"a\"");
        result.ShouldContain("2");
        result.ShouldContain("0x3"); // Property2 使用十六进制
        result.ShouldContain("false");

        // 验证格式正确
        result.ShouldStartWith("{");
        result.ShouldEndWith("}");
    }

    #endregion

    #region 格式化缩进测试

    [Fact]
    public void FormatCinValueWithIndent_Level0_ShouldCompressToSingleLine()
    {
        // 测试压缩为单行
        var multilineValue = @"{
  1,
  2,
  {
    3,
    4
  }
}";
        var result = CinTypeUtils.FormatCinValueWithIndent(multilineValue, 0);
        result.ShouldBe("{1,2,{3,4}}");
    }

    [Fact]
    public void FormatCinValueWithIndent_Level1_ShouldFormatWithLevel1Indent()
    {
        // 测试层级1缩进
        var singleLineValue = "{1,2,{3,4}}";
        var result = CinTypeUtils.FormatCinValueWithIndent(singleLineValue, 1);

        var expected = @"{
  1,
  2,
  {3, 4}
}";
        result.ShouldBe(expected.Replace("\r\n", "\n"));
    }

    [Fact]
    public void FormatCinValueWithIndent_Level2_ShouldFormatWithLevel2Indent()
    {
        // 测试层级2缩进
        var singleLineValue = "{1,2,{3,4,{5,6}}}";
        var result = CinTypeUtils.FormatCinValueWithIndent(singleLineValue, 2);

        var expected = @"{
  1,
  2,
  {
    3,
    4,
    {5, 6}
  }
}";
        result.ShouldBe(expected.Replace("\r\n", "\n"));
    }

    [Fact]
    public void FormatCinValueWithIndent_SimpleValue_ShouldReturnAsIs()
    {
        // 测试简单值（不包含大括号）
        var simpleValue = "123";
        var result = CinTypeUtils.FormatCinValueWithIndent(simpleValue, 1);
        result.ShouldBe("123");
    }

    [Fact]
    public void FormatCinValueWithIndent_EmptyValue_ShouldReturnEmpty()
    {
        // 测试空值
        var result = CinTypeUtils.FormatCinValueWithIndent("", 1);
        result.ShouldBe("");

        var nullResult = CinTypeUtils.FormatCinValueWithIndent(null, 1);
        nullResult.ShouldBeNull();
    }

    [Fact]
    public void FormatCinValueWithIndent_ComplexStruct_ShouldFormatCorrectly()
    {
        // 测试复杂结构体格式化
        var complexValue = "{{0x120,65},116,{{12,4},{8,4},{20,4}}}";
        var result = CinTypeUtils.FormatCinValueWithIndent(complexValue, 1);

        var expected = @"{
  {0x120, 65},
  116,
  {{12, 4}, {8, 4}, {20, 4}}
}";
        result.ShouldBe(expected.Replace("\r\n", "\n"));
    }

    #endregion
}
