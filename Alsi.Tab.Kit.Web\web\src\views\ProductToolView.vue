<template>
  <div class="product-tool">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>产品工具</h1>
      <p>管理产品版本发布</p>
    </div>

    <!-- Cloud 地址配置 -->
    <el-card class="config-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>Cloud 配置</span>
        </div>
      </template>
      <div class="config-content">
        <div class="config-row">
          <label>Cloud 地址</label>
          <el-input 
            v-model="baseUrl" 
            placeholder="请输入 Cloud 地址" 
            style="width: 400px;"
            @change="loadProducts"
          />
          <el-button type="primary" @click="loadProducts" :loading="loading">刷新</el-button>
        </div>
      </div>
    </el-card>

    <!-- 产品列表 -->
    <el-card class="products-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>产品列表</span>
        </div>
      </template>
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else-if="products.length === 0" class="empty-state">
        <el-empty description="暂无产品数据" />
      </div>
      <div v-else>
        <el-table :data="products" style="width: 100%" @row-click="selectProduct">
          <el-table-column prop="name" label="产品名称" width="200" />
          <el-table-column prop="latestVersion" label="最新版本" width="120" />
          <el-table-column prop="latestChangelog" label="最新更新日志" show-overflow-tooltip />
          <el-table-column prop="creationTime" label="创建时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.creationTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="latestPublishTime" label="最新发布时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.latestPublishTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="primary" size="small" @click.stop="selectProduct(scope.row)">
                查看版本
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 版本列表 -->
    <el-card v-if="selectedProduct" class="versions-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>{{ selectedProduct.name }} - 版本列表</span>
        </div>
      </template>
      <div v-if="versionsLoading" class="loading-container">
        <el-skeleton :rows="2" animated />
      </div>
      <div v-else-if="versions.length === 0" class="empty-state">
        <el-empty description="暂无版本数据" />
      </div>
      <div v-else>
        <el-table :data="versions" style="width: 100%">
          <el-table-column prop="version" label="版本号" width="120" />
          <el-table-column prop="changeLog" label="更新日志" show-overflow-tooltip />
          <el-table-column prop="releaseTime" label="发布时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.releaseTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="creationTime" label="创建时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.creationTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 发布新版本 -->
    <el-card class="publish-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>发布新版本</span>
        </div>
      </template>
      <div class="publish-content">
        <div class="form-row">
          <label>产品名称</label>
          <el-input 
            v-model="publishForm.productName" 
            placeholder="请输入产品名称"
            style="width: 300px;"
          />
        </div>
        <div class="form-row">
          <label>版本号</label>
          <el-input 
            v-model="publishForm.version" 
            placeholder="请输入版本号"
            style="width: 300px;"
          />
        </div>
        <div class="form-row">
          <label>ZIP 文件</label>
          <el-input 
            v-model="publishForm.zipPath" 
            placeholder="请选择 ZIP 文件"
            style="width: 300px;"
            readonly
          />
          <el-button @click="selectZipFile" :loading="selecting">选择文件</el-button>
        </div>
        <div class="form-row">
          <label>更新日志</label>
          <el-input 
            v-model="publishForm.changelog" 
            type="textarea"
            :rows="4"
            placeholder="请输入更新日志（可选）"
            style="width: 500px;"
          />
        </div>
        <div class="form-row">
          <el-button 
            type="primary" 
            @click="publishVersion" 
            :loading="publishing"
            :disabled="!canPublish"
          >
            发布版本
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { appApi, ProductDto, ProductVersionDto, ProductVersionZipRequest } from "@/api/appApi";

export default defineComponent({
  name: "ProductToolView",
  setup() {
    // 数据
    const baseUrl = ref("http://***********:5000/");
    const loading = ref(false);
    const versionsLoading = ref(false);
    const selecting = ref(false);
    const publishing = ref(false);
    
    const products = ref<ProductDto[]>([]);
    const selectedProduct = ref<ProductDto | null>(null);
    const versions = ref<ProductVersionDto[]>([]);
    
    const publishForm = ref<ProductVersionZipRequest>({
      baseUrl: "",
      productName: "TabKit",
      zipPath: "",
      version: "",
      changelog: ""
    });

    // 计算属性
    const canPublish = computed(() => {
      return baseUrl.value && 
             publishForm.value.productName && 
             publishForm.value.version && 
             publishForm.value.zipPath;
    });

    // 方法
    const loadProducts = async () => {
      if (!baseUrl.value) {
        ElMessage.warning("请输入 Cloud 地址");
        return;
      }

      loading.value = true;
      try {
        const response = await appApi.product.getProducts(baseUrl.value);
        products.value = response.data;
        publishForm.value.baseUrl = baseUrl.value;
      } catch (error) {
        console.error("加载产品列表失败:", error);
        ElMessage.error("加载产品列表失败");
      } finally {
        loading.value = false;
      }
    };

    const selectProduct = async (product: ProductDto) => {
      selectedProduct.value = product;
      versionsLoading.value = true;
      
      try {
        const response = await appApi.product.getProductVersions(baseUrl.value, product.id);
        versions.value = response.data;
      } catch (error) {
        console.error("加载版本列表失败:", error);
        ElMessage.error("加载版本列表失败");
      } finally {
        versionsLoading.value = false;
      }
    };

    const selectZipFile = async () => {
      selecting.value = true;
      try {
        const response = await appApi.product.selectZipFile();
        publishForm.value.zipPath = response.data;
      } catch (error) {
        console.error("选择文件失败:", error);
        ElMessage.error("选择文件失败");
      } finally {
        selecting.value = false;
      }
    };

    const publishVersion = async () => {
      if (!canPublish.value) {
        ElMessage.warning("请填写完整信息");
        return;
      }

      publishing.value = true;
      try {
        await appApi.product.publishVersion(publishForm.value);
        ElMessage.success("版本发布成功");
        
        // 重新加载产品列表
        await loadProducts();
        
        // 如果有选中的产品，重新加载版本列表
        if (selectedProduct.value) {
          await selectProduct(selectedProduct.value);
        }
        
        // 清空表单
        publishForm.value.version = "";
        publishForm.value.zipPath = "";
        publishForm.value.changelog = "";
      } catch (error) {
        console.error("发布版本失败:", error);
        ElMessage.error("发布版本失败");
      } finally {
        publishing.value = false;
      }
    };

    const formatDateTime = (dateTime: string | null | undefined) => {
      if (!dateTime) return "-";
      return new Date(dateTime).toLocaleString("zh-CN");
    };

    // 生命周期
    onMounted(() => {
      publishForm.value.baseUrl = baseUrl.value;
    });

    return {
      baseUrl,
      loading,
      versionsLoading,
      selecting,
      publishing,
      products,
      selectedProduct,
      versions,
      publishForm,
      canPublish,
      loadProducts,
      selectProduct,
      selectZipFile,
      publishVersion,
      formatDateTime
    };
  }
});
</script>

<style scoped>
.product-tool {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.config-card,
.products-card,
.versions-card,
.publish-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.config-content {
  padding: 10px 0;
}

.config-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.config-row label {
  min-width: 80px;
  font-weight: 500;
  color: #606266;
}

.loading-container {
  padding: 20px;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.form-row {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.form-row label {
  min-width: 80px;
  padding-top: 8px;
  font-weight: 500;
  color: #606266;
}

.publish-content {
  padding: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-tool {
    padding: 10px;
  }

  .config-row,
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .config-row label,
  .form-row label {
    min-width: auto;
    padding-top: 0;
  }
}
</style>
