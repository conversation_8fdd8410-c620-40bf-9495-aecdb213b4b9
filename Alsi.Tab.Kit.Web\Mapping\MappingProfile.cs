using Alsi.Tab.Kit.Core.Models;
using Alsi.Tab.Kit.Core.Services.Product;
using Alsi.Tab.Kit.Web.Controllers;
using Alsi.Tab.Kit.Web.Dto;
using AutoMapper;

namespace Alsi.Tab.Kit.Web.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<TestMode, TestModeDto>();

            CreateMap<AppEntry, AppEntryDto>()
                .ForMember(dest => dest.IconPath, opt => opt.MapFrom(src => src.FullIconPath))
                .ForMember(dest => dest.AppType, opt => opt.MapFrom(src => src.AppType))
                .ForMember(dest => dest.ShowInTopMenu, opt => opt.MapFrom(src => src.ShowInTopMenu))
                .ForMember(dest => dest.ShowInBottomMenu, opt => opt.MapFrom(src => src.ShowInBottomMenu))
                .ForMember(dest => dest.Icon, opt => opt.MapFrom(src => src.Icon));

            CreateMap<CinTemplate, CinTemplateDto>();

            CreateMap<SourceFile, SourceFileDto>();
            CreateMap<ParsedParam, ParsedParamDto>();

            // Product related mappings
            CreateMap<ProductVersionZipRequestDto, ProductVersionZipRequest>();
        }
    }
}
