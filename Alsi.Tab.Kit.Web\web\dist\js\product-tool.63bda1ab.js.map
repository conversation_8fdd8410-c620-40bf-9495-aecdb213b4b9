{"version": 3, "file": "js/product-tool.63bda1ab.js", "mappings": "oMAEA,MAAMA,EAAa,CCDZC,MAAM,gBDEPC,EAAa,CCYRD,MAAM,kBDXXE,EAAa,CCYNF,MAAM,cDXbG,EAAa,CACjBC,IAAK,EC8BmBJ,MAAM,qBD3B1BK,EAAa,CACjBD,IAAK,EC6BsCJ,MAAM,eD1B7CM,EAAa,CAAEF,IAAK,GACpBG,EAAa,CCyDNP,MAAM,eDxDbQ,EAAa,CACjBJ,IAAK,EC2D2BJ,MAAM,qBDxDlCS,EAAa,CACjBL,IAAK,EC0DsCJ,MAAM,eDvD7CU,EAAc,CAAEN,IAAK,GACrBO,EAAc,CCkFTX,MAAM,mBDjFXY,EAAc,CCkFPZ,MAAM,YDjFba,EAAc,CCyFPb,MAAM,YDxFbc,EAAc,CCgGPd,MAAM,YD/Fbe,EAAc,CCyGPf,MAAM,YDxGbgB,EAAc,CCkHPhB,MAAM,YDhHb,SAAUiB,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAsBC,EAAAA,EAAAA,IAAkB,YACxCC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCE,GAAqBF,EAAAA,EAAAA,IAAkB,WACvCG,GAAyBH,EAAAA,EAAAA,IAAkB,eAC3CI,GAAsBJ,EAAAA,EAAAA,IAAkB,YACxCK,GAA6BL,EAAAA,EAAAA,IAAkB,mBAC/CM,GAAsBN,EAAAA,EAAAA,IAAkB,YAE9C,OAAQO,EAAAA,EAAAA,OCvCRC,EAAAA,EAAAA,IA0JM,MA1JNlC,EA0JM,CDlHJoB,EAAO,MAAQA,EAAO,KCtCtBe,EAAAA,EAAAA,IAGM,OAHDlC,MAAM,eAAa,EACtBkC,EAAAA,EAAAA,IAAa,UAAT,SACJA,EAAAA,EAAAA,IAAe,SAAZ,cDuCD,KCnCJC,EAAAA,EAAAA,IAkBUR,EAAA,CAlBD3B,MAAM,cAAcoC,OAAO,SDuCjC,CCtCUC,QAAMC,EAAAA,EAAAA,IACf,IAEMnB,EAAA,KAAAA,EAAA,KAFNe,EAAAA,EAAAA,IAEM,OAFDlC,MAAM,eAAa,EACtBkC,EAAAA,EAAAA,IAAqB,YAAf,cDwCJ,MAENK,SAASD,EAAAA,EAAAA,ICvCT,IAWM,EAXNJ,EAAAA,EAAAA,IAWM,MAXNjC,EAWM,EAVJiC,EAAAA,EAAAA,IASM,MATNhC,EASM,CDgCFiB,EAAO,KAAOA,EAAO,ICxCvBe,EAAAA,EAAAA,IAAuB,aAAhB,YAAQ,KACfC,EAAAA,EAAAA,IAKEX,EAAA,CDoCEgB,WCxCOtB,EAAAuB,QDyCP,sBAAuBtB,EAAO,KAAOA,EAAO,GAAMuB,GCzC3CxB,EAAAuB,QAAOC,GAChBC,YAAY,eACZC,MAAA,gBACCC,SAAQ3B,EAAA4B,cD0CN,KAAM,EAAG,CAAC,aAAc,cCxC7BX,EAAAA,EAAAA,IAAiFT,EAAA,CAAtEqB,KAAK,UAAWC,QAAO9B,EAAA4B,aAAeG,QAAS/B,EAAA+B,SD6CrD,CACDV,SAASD,EAAAA,EAAAA,IC9CsD,IAAEnB,EAAA,KAAAA,EAAA,KD+C/D+B,EAAAA,EAAAA,IC/C6D,SDiD/DC,EAAG,EACHC,GAAI,CAAC,IACJ,EAAG,CAAC,UAAW,kBAIxBD,EAAG,KCjDLhB,EAAAA,EAAAA,IAoCUR,EAAA,CApCD3B,MAAM,gBAAgBoC,OAAO,SDsDnC,CCrDUC,QAAMC,EAAAA,EAAAA,IACf,IAEMnB,EAAA,KAAAA,EAAA,KAFNe,EAAAA,EAAAA,IAEM,OAFDlC,MAAM,eAAa,EACtBkC,EAAAA,EAAAA,IAAiB,YAAX,UDuDJ,MAENK,SAASD,EAAAA,EAAAA,ICtDT,IAEM,CAFKpB,EAAA+B,UDwDJjB,EAAAA,EAAAA,OCxDPC,EAAAA,EAAAA,IAEM,MAFN9B,EAEM,EADJgC,EAAAA,EAAAA,IAAkCP,EAAA,CAApByB,KAAM,EAAGC,SAAA,QAEW,IAApBpC,EAAAqC,SAASC,SD4DhBxB,EAAAA,EAAAA,OC5DTC,EAAAA,EAAAA,IAEM,MAFN5B,EAEM,EADJ8B,EAAAA,EAAAA,IAAiCN,EAAA,CAAvB4B,YAAY,gBD8DfzB,EAAAA,EAAAA,OC5DTC,EAAAA,EAAAA,IAuBM,MAAA3B,EAAA,EAtBJ6B,EAAAA,EAAAA,IAqBWJ,EAAA,CArBA2B,KAAMxC,EAAAqC,SAAUX,MAAA,eAAqBe,WAAWzC,EAAA0C,eDgEhD,CACDrB,SAASD,EAAAA,EAAAA,IChEjB,IAAwD,EAAxDH,EAAAA,EAAAA,IAAwDL,EAAA,CAAvC+B,KAAK,OAAOC,MAAM,OAAOC,MAAM,SAChD5B,EAAAA,EAAAA,IAAiEL,EAAA,CAAhD+B,KAAK,gBAAgBC,MAAM,OAAOC,MAAM,SACzD5B,EAAAA,EAAAA,IAA+EL,EAAA,CAA9D+B,KAAK,kBAAkBC,MAAM,SAAS,8BACvD3B,EAAAA,EAAAA,IAIkBL,EAAA,CAJD+B,KAAK,eAAeC,MAAM,OAAOC,MAAM,ODiF3C,CChFAxB,SAAOD,EAAAA,EAAAA,IAC4B0B,GADrB,EDkFbd,EAAAA,EAAAA,KAAiBe,EAAAA,EAAAA,ICjFxB/C,EAAAgD,eAAeF,EAAMG,IAAIC,eAAY,KDmFhCjB,EAAG,KChFfhB,EAAAA,EAAAA,IAIkBL,EAAA,CAJD+B,KAAK,oBAAoBC,MAAM,SAASC,MAAM,ODsFlD,CCrFAxB,SAAOD,EAAAA,EAAAA,IACiC0B,GAD1B,EDuFbd,EAAAA,EAAAA,KAAiBe,EAAAA,EAAAA,ICtFxB/C,EAAAgD,eAAeF,EAAMG,IAAIE,oBAAiB,KDwFrClB,EAAG,KCrFfhB,EAAAA,EAAAA,IAMkBL,EAAA,CANDgC,MAAM,KAAKC,MAAM,OD0FrB,CCzFAxB,SAAOD,EAAAA,EAAAA,IAGJ0B,GAHW,EACvB7B,EAAAA,EAAAA,IAEYT,EAAA,CAFDqB,KAAK,UAAUuB,KAAK,QAAStB,SAAKuB,EAAAA,EAAAA,IAAA7B,GAAOxB,EAAA0C,cAAcI,EAAMG,KAAG,WD8F9D,CACD5B,SAASD,EAAAA,EAAAA,IC/FyD,IAE9EnB,EAAA,KAAAA,EAAA,KD8Fc+B,EAAAA,EAAAA,IChGgE,aDkGlEC,EAAG,EACHC,GAAI,CAAC,IACJ,KAAM,CAAC,cAEZD,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,OAAQ,oBAGzBA,EAAG,ICnGUjC,EAAAsD,kBDsGVxC,EAAAA,EAAAA,OCtGLyC,EAAAA,EAAAA,IA4BU9C,EAAA,CD2EJvB,IAAK,ECvGqBJ,MAAM,gBAAgBoC,OAAO,SD0GtD,CCzGMC,QAAMC,EAAAA,EAAAA,IACf,IAEM,EAFNJ,EAAAA,EAAAA,IAEM,MAFN3B,EAEM,EADJ2B,EAAAA,EAAAA,IAA8C,aAAA+B,EAAAA,EAAAA,IAArC/C,EAAAsD,gBAAgBE,MAAO,UAAO,OD6GvCnC,SAASD,EAAAA,EAAAA,IC1Gb,IAEM,CAFKpB,EAAAyD,kBD4GA3C,EAAAA,EAAAA,OC5GXC,EAAAA,EAAAA,IAEM,MAFNzB,EAEM,EADJ2B,EAAAA,EAAAA,IAAkCP,EAAA,CAApByB,KAAM,EAAGC,SAAA,QAEW,IAApBpC,EAAA0D,SAASpB,SDgHZxB,EAAAA,EAAAA,OChHbC,EAAAA,EAAAA,IAEM,MAFNxB,EAEM,EADJ0B,EAAAA,EAAAA,IAAiCN,EAAA,CAAvB4B,YAAY,gBDkHXzB,EAAAA,EAAAA,OChHbC,EAAAA,EAAAA,IAeM,MAAAvB,EAAA,EAdJyB,EAAAA,EAAAA,IAaWJ,EAAA,CAbA2B,KAAMxC,EAAA0D,SAAUhC,MAAA,gBDmHZ,CACDL,SAASD,EAAAA,EAAAA,ICnHrB,IAA0D,EAA1DH,EAAAA,EAAAA,IAA0DL,EAAA,CAAzC+B,KAAK,UAAUC,MAAM,MAAMC,MAAM,SAClD5B,EAAAA,EAAAA,IAAuEL,EAAA,CAAtD+B,KAAK,YAAYC,MAAM,OAAO,8BAC/C3B,EAAAA,EAAAA,IAIkBL,EAAA,CAJD+B,KAAK,cAAcC,MAAM,OAAOC,MAAM,ODgItC,CC/HJxB,SAAOD,EAAAA,EAAAA,IAC2B0B,GADpB,EDiITd,EAAAA,EAAAA,KAAiBe,EAAAA,EAAAA,IChI5B/C,EAAAgD,eAAeF,EAAMG,IAAIU,cAAW,KDkI3B1B,EAAG,KC/HnBhB,EAAAA,EAAAA,IAIkBL,EAAA,CAJD+B,KAAK,eAAeC,MAAM,OAAOC,MAAM,ODqIvC,CCpIJxB,SAAOD,EAAAA,EAAAA,IAC4B0B,GADrB,EDsITd,EAAAA,EAAAA,KAAiBe,EAAAA,EAAAA,ICrI5B/C,EAAAgD,eAAeF,EAAMG,IAAIC,eAAY,KDuI5BjB,EAAG,MAGPA,EAAG,GACF,EAAG,CAAC,cAGjBA,EAAG,MAEL2B,EAAAA,EAAAA,IAAoB,IAAI,ICxI5B3C,EAAAA,EAAAA,IAsDUR,EAAA,CAtDD3B,MAAM,eAAeoC,OAAO,SD4IlC,CC3IUC,QAAMC,EAAAA,EAAAA,IACf,IAEMnB,EAAA,MAAAA,EAAA,MAFNe,EAAAA,EAAAA,IAEM,OAFDlC,MAAM,eAAa,EACtBkC,EAAAA,EAAAA,IAAkB,YAAZ,WD6IJ,MAENK,SAASD,EAAAA,EAAAA,IC5IT,IA+CM,EA/CNJ,EAAAA,EAAAA,IA+CM,MA/CNvB,EA+CM,EA9CJuB,EAAAA,EAAAA,IAOM,MAPNtB,EAOM,CDuIFO,EAAO,MAAQA,EAAO,KC7IxBe,EAAAA,EAAAA,IAAmB,aAAZ,QAAI,KACXC,EAAAA,EAAAA,IAIEX,EAAA,CD0IEgB,WC7IOtB,EAAA6D,YAAYC,YD8InB,sBAAuB7D,EAAO,KAAOA,EAAO,GAAMuB,GC9I3CxB,EAAA6D,YAAYC,YAAWtC,GAChCC,YAAY,UACZC,MAAA,iBD+IG,KAAM,EAAG,CAAC,kBC5IjBV,EAAAA,EAAAA,IAOM,MAPNrB,EAOM,CDwIFM,EAAO,MAAQA,EAAO,KC9IxBe,EAAAA,EAAAA,IAAkB,aAAX,OAAG,KACVC,EAAAA,EAAAA,IAIEX,EAAA,CD2IEgB,WC9IOtB,EAAA6D,YAAYE,QD+InB,sBAAuB9D,EAAO,KAAOA,EAAO,GAAMuB,GC/I3CxB,EAAA6D,YAAYE,QAAOvC,GAC5BC,YAAY,SACZC,MAAA,iBDgJG,KAAM,EAAG,CAAC,kBC7IjBV,EAAAA,EAAAA,IASM,MATNpB,EASM,CDuIFK,EAAO,MAAQA,EAAO,KC/IxBe,EAAAA,EAAAA,IAAqB,aAAd,UAAM,KACbC,EAAAA,EAAAA,IAKEX,EAAA,CD2IEgB,WC/IOtB,EAAA6D,YAAYG,QDgJnB,sBAAuB/D,EAAO,KAAOA,EAAO,GAAMuB,GChJ3CxB,EAAA6D,YAAYG,QAAOxC,GAC5BC,YAAY,aACZC,MAAA,gBACAuC,SAAA,IDiJG,KAAM,EAAG,CAAC,gBC/IfhD,EAAAA,EAAAA,IAAuET,EAAA,CAA3DsB,QAAO9B,EAAAkE,cAAgBnC,QAAS/B,EAAAmE,WDmJvC,CACD9C,SAASD,EAAAA,EAAAA,ICpJ0C,IAAInB,EAAA,MAAAA,EAAA,MDqJrD+B,EAAAA,EAAAA,ICrJiD,WDuJnDC,EAAG,EACHC,GAAI,CAAC,KACJ,EAAG,CAAC,UAAW,eCvJtBlB,EAAAA,EAAAA,IASM,MATNnB,EASM,CDiJFI,EAAO,MAAQA,EAAO,KCzJxBe,EAAAA,EAAAA,IAAmB,aAAZ,QAAI,KACXC,EAAAA,EAAAA,IAMEX,EAAA,CDoJEgB,WCzJOtB,EAAA6D,YAAYO,UD0JnB,sBAAuBnE,EAAO,KAAOA,EAAO,GAAMuB,GC1J3CxB,EAAA6D,YAAYO,UAAS5C,GAC9BK,KAAK,WACJM,KAAM,EACPV,YAAY,cACZC,MAAA,iBD2JG,KAAM,EAAG,CAAC,kBCxJjBV,EAAAA,EAAAA,IASM,MATNlB,EASM,EARJmB,EAAAA,EAAAA,IAOYT,EAAA,CANVqB,KAAK,UACJC,QAAO9B,EAAAqE,eACPtC,QAAS/B,EAAAsE,WACTC,UAAWvE,EAAAwE,YD2JT,CACDnD,SAASD,EAAAA,EAAAA,IC3JZ,IAEDnB,EAAA,MAAAA,EAAA,MD0JM+B,EAAAA,EAAAA,IC5JL,aD8JGC,EAAG,EACHC,GAAI,CAAC,KACJ,EAAG,CAAC,UAAW,UAAW,mBAInCD,EAAG,KAGT,C,iCCzJA,GAAewC,EAAAA,EAAAA,IAAgB,CAC7BjB,KAAM,kBACNkB,KAAAA,GAEE,MAAMnD,GAAUoD,EAAAA,EAAAA,IAAI,4BACd5C,GAAU4C,EAAAA,EAAAA,KAAI,GACdlB,GAAkBkB,EAAAA,EAAAA,KAAI,GACtBR,GAAYQ,EAAAA,EAAAA,KAAI,GAChBL,GAAaK,EAAAA,EAAAA,KAAI,GAEjBtC,GAAWsC,EAAAA,EAAAA,IAAkB,IAC7BrB,GAAkBqB,EAAAA,EAAAA,IAAuB,MACzCjB,GAAWiB,EAAAA,EAAAA,IAAyB,IAEpCd,GAAcc,EAAAA,EAAAA,IAA8B,CAChDpD,QAAS,GACTuC,YAAa,SACbE,QAAS,GACTD,QAAS,GACTK,UAAW,KAIPI,GAAaI,EAAAA,EAAAA,IAAS,IACnBrD,EAAQsD,OACRhB,EAAYgB,MAAMf,aAClBD,EAAYgB,MAAMd,SAClBF,EAAYgB,MAAMb,SAIrBpC,EAAekD,UACnB,GAAKvD,EAAQsD,MAAb,CAKA9C,EAAQ8C,OAAQ,EAChB,IACE,MAAME,QAAiBC,EAAAA,GAAOC,QAAQC,YAAY3D,EAAQsD,OAC1DxC,EAASwC,MAAQE,EAASvC,KAC1BqB,EAAYgB,MAAMtD,QAAUA,EAAQsD,K,CACpC,MAAOM,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,W,CAClB,QACEpD,EAAQ8C,OAAQ,C,OAbhBQ,EAAAA,GAAUC,QAAQ,iBAiBhB5C,EAAgBoC,UACpBxB,EAAgBuB,MAAQI,EACxBxB,EAAgBoB,OAAQ,EAExB,IACE,MAAME,QAAiBC,EAAAA,GAAOC,QAAQM,mBAAmBhE,EAAQsD,MAAOI,EAAQO,IAChF9B,EAASmB,MAAQE,EAASvC,I,CAC1B,MAAO2C,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,W,CAClB,QACE1B,EAAgBoB,OAAQ,C,GAItBX,EAAgBY,UACpBX,EAAUU,OAAQ,EAClB,IACE,MAAME,QAAiBC,EAAAA,GAAOC,QAAQf,gBACtCL,EAAYgB,MAAMb,QAAUe,EAASvC,I,CACrC,MAAO2C,GACPC,QAAQD,MAAM,UAAWA,GACzBE,EAAAA,GAAUF,MAAM,S,CAClB,QACEhB,EAAUU,OAAQ,C,GAIhBR,EAAiBS,UACrB,GAAKN,EAAWK,MAAhB,CAKAP,EAAWO,OAAQ,EACnB,UACQG,EAAAA,GAAOC,QAAQZ,eAAeR,EAAYgB,OAChDQ,EAAAA,GAAUI,QAAQ,gBAGZ7D,IAGF0B,EAAgBuB,aACZnC,EAAcY,EAAgBuB,OAItChB,EAAYgB,MAAMd,QAAU,GAC5BF,EAAYgB,MAAMb,QAAU,GAC5BH,EAAYgB,MAAMT,UAAY,E,CAC9B,MAAOe,GACPC,QAAQD,MAAM,UAAWA,GACzBE,EAAAA,GAAUF,MAAM,S,CAClB,QACEb,EAAWO,OAAQ,C,OAzBnBQ,EAAAA,GAAUC,QAAQ,YA6BhBtC,EAAkB0C,GACjBA,EACE,IAAIC,KAAKD,GAAUE,eAAe,SADnB,IASxB,OAJAC,EAAAA,EAAAA,IAAU,KACRhC,EAAYgB,MAAMtD,QAAUA,EAAQsD,QAG/B,CACLtD,UACAQ,UACA0B,kBACAU,YACAG,aACAjC,WACAiB,kBACAI,WACAG,cACAW,aACA5C,eACAc,gBACAwB,gBACAG,iBACArB,iBAEJ,I,UCpSF,MAAM8C,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS/F,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://tab-kit-web/./src/views/ProductToolView.vue?b18f", "webpack://tab-kit-web/./src/views/ProductToolView.vue", "webpack://tab-kit-web/./src/views/ProductToolView.vue?c1b2"], "sourcesContent": ["import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, withModifiers as _withModifiers, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"product-tool\" }\nconst _hoisted_2 = { class: \"config-content\" }\nconst _hoisted_3 = { class: \"config-row\" }\nconst _hoisted_4 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_5 = {\n  key: 1,\n  class: \"empty-state\"\n}\nconst _hoisted_6 = { key: 2 }\nconst _hoisted_7 = { class: \"card-header\" }\nconst _hoisted_8 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_9 = {\n  key: 1,\n  class: \"empty-state\"\n}\nconst _hoisted_10 = { key: 2 }\nconst _hoisted_11 = { class: \"publish-content\" }\nconst _hoisted_12 = { class: \"form-row\" }\nconst _hoisted_13 = { class: \"form-row\" }\nconst _hoisted_14 = { class: \"form-row\" }\nconst _hoisted_15 = { class: \"form-row\" }\nconst _hoisted_16 = { class: \"form-row\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _cache[17] || (_cache[17] = _createElementVNode(\"div\", { class: \"page-header\" }, [\n      _createElementVNode(\"h1\", null, \"产品工具\"),\n      _createElementVNode(\"p\", null, \"管理产品版本发布\")\n    ], -1)),\n    _createVNode(_component_el_card, {\n      class: \"config-card\",\n      shadow: \"never\"\n    }, {\n      header: _withCtx(() => _cache[5] || (_cache[5] = [\n        _createElementVNode(\"div\", { class: \"card-header\" }, [\n          _createElementVNode(\"span\", null, \"Cloud 配置\")\n        ], -1)\n      ])),\n      default: _withCtx(() => [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _createElementVNode(\"div\", _hoisted_3, [\n            _cache[7] || (_cache[7] = _createElementVNode(\"label\", null, \"Cloud 地址\", -1)),\n            _createVNode(_component_el_input, {\n              modelValue: _ctx.baseUrl,\n              \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((_ctx.baseUrl) = $event)),\n              placeholder: \"请输入 Cloud 地址\",\n              style: {\"width\":\"400px\"},\n              onChange: _ctx.loadProducts\n            }, null, 8, [\"modelValue\", \"onChange\"]),\n            _createVNode(_component_el_button, {\n              type: \"primary\",\n              onClick: _ctx.loadProducts,\n              loading: _ctx.loading\n            }, {\n              default: _withCtx(() => _cache[6] || (_cache[6] = [\n                _createTextVNode(\"刷新\")\n              ])),\n              _: 1,\n              __: [6]\n            }, 8, [\"onClick\", \"loading\"])\n          ])\n        ])\n      ]),\n      _: 1\n    }),\n    _createVNode(_component_el_card, {\n      class: \"products-card\",\n      shadow: \"never\"\n    }, {\n      header: _withCtx(() => _cache[8] || (_cache[8] = [\n        _createElementVNode(\"div\", { class: \"card-header\" }, [\n          _createElementVNode(\"span\", null, \"产品列表\")\n        ], -1)\n      ])),\n      default: _withCtx(() => [\n        (_ctx.loading)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n              _createVNode(_component_el_skeleton, {\n                rows: 3,\n                animated: \"\"\n              })\n            ]))\n          : (_ctx.products.length === 0)\n            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n                _createVNode(_component_el_empty, { description: \"暂无产品数据\" })\n              ]))\n            : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n                _createVNode(_component_el_table, {\n                  data: _ctx.products,\n                  style: {\"width\":\"100%\"},\n                  onRowClick: _ctx.selectProduct\n                }, {\n                  default: _withCtx(() => [\n                    _createVNode(_component_el_table_column, {\n                      prop: \"name\",\n                      label: \"产品名称\",\n                      width: \"200\"\n                    }),\n                    _createVNode(_component_el_table_column, {\n                      prop: \"latestVersion\",\n                      label: \"最新版本\",\n                      width: \"120\"\n                    }),\n                    _createVNode(_component_el_table_column, {\n                      prop: \"latestChangelog\",\n                      label: \"最新更新日志\",\n                      \"show-overflow-tooltip\": \"\"\n                    }),\n                    _createVNode(_component_el_table_column, {\n                      prop: \"creationTime\",\n                      label: \"创建时间\",\n                      width: \"180\"\n                    }, {\n                      default: _withCtx((scope) => [\n                        _createTextVNode(_toDisplayString(_ctx.formatDateTime(scope.row.creationTime)), 1)\n                      ]),\n                      _: 1\n                    }),\n                    _createVNode(_component_el_table_column, {\n                      prop: \"latestPublishTime\",\n                      label: \"最新发布时间\",\n                      width: \"180\"\n                    }, {\n                      default: _withCtx((scope) => [\n                        _createTextVNode(_toDisplayString(_ctx.formatDateTime(scope.row.latestPublishTime)), 1)\n                      ]),\n                      _: 1\n                    }),\n                    _createVNode(_component_el_table_column, {\n                      label: \"操作\",\n                      width: \"120\"\n                    }, {\n                      default: _withCtx((scope) => [\n                        _createVNode(_component_el_button, {\n                          type: \"primary\",\n                          size: \"small\",\n                          onClick: _withModifiers(($event: any) => (_ctx.selectProduct(scope.row)), [\"stop\"])\n                        }, {\n                          default: _withCtx(() => _cache[9] || (_cache[9] = [\n                            _createTextVNode(\" 查看版本 \")\n                          ])),\n                          _: 2,\n                          __: [9]\n                        }, 1032, [\"onClick\"])\n                      ]),\n                      _: 1\n                    })\n                  ]),\n                  _: 1\n                }, 8, [\"data\", \"onRowClick\"])\n              ]))\n      ]),\n      _: 1\n    }),\n    (_ctx.selectedProduct)\n      ? (_openBlock(), _createBlock(_component_el_card, {\n          key: 0,\n          class: \"versions-card\",\n          shadow: \"never\"\n        }, {\n          header: _withCtx(() => [\n            _createElementVNode(\"div\", _hoisted_7, [\n              _createElementVNode(\"span\", null, _toDisplayString(_ctx.selectedProduct.name) + \" - 版本列表\", 1)\n            ])\n          ]),\n          default: _withCtx(() => [\n            (_ctx.versionsLoading)\n              ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [\n                  _createVNode(_component_el_skeleton, {\n                    rows: 2,\n                    animated: \"\"\n                  })\n                ]))\n              : (_ctx.versions.length === 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [\n                    _createVNode(_component_el_empty, { description: \"暂无版本数据\" })\n                  ]))\n                : (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [\n                    _createVNode(_component_el_table, {\n                      data: _ctx.versions,\n                      style: {\"width\":\"100%\"}\n                    }, {\n                      default: _withCtx(() => [\n                        _createVNode(_component_el_table_column, {\n                          prop: \"version\",\n                          label: \"版本号\",\n                          width: \"120\"\n                        }),\n                        _createVNode(_component_el_table_column, {\n                          prop: \"changeLog\",\n                          label: \"更新日志\",\n                          \"show-overflow-tooltip\": \"\"\n                        }),\n                        _createVNode(_component_el_table_column, {\n                          prop: \"releaseTime\",\n                          label: \"发布时间\",\n                          width: \"180\"\n                        }, {\n                          default: _withCtx((scope) => [\n                            _createTextVNode(_toDisplayString(_ctx.formatDateTime(scope.row.releaseTime)), 1)\n                          ]),\n                          _: 1\n                        }),\n                        _createVNode(_component_el_table_column, {\n                          prop: \"creationTime\",\n                          label: \"创建时间\",\n                          width: \"180\"\n                        }, {\n                          default: _withCtx((scope) => [\n                            _createTextVNode(_toDisplayString(_ctx.formatDateTime(scope.row.creationTime)), 1)\n                          ]),\n                          _: 1\n                        })\n                      ]),\n                      _: 1\n                    }, 8, [\"data\"])\n                  ]))\n          ]),\n          _: 1\n        }))\n      : _createCommentVNode(\"\", true),\n    _createVNode(_component_el_card, {\n      class: \"publish-card\",\n      shadow: \"never\"\n    }, {\n      header: _withCtx(() => _cache[10] || (_cache[10] = [\n        _createElementVNode(\"div\", { class: \"card-header\" }, [\n          _createElementVNode(\"span\", null, \"发布新版本\")\n        ], -1)\n      ])),\n      default: _withCtx(() => [\n        _createElementVNode(\"div\", _hoisted_11, [\n          _createElementVNode(\"div\", _hoisted_12, [\n            _cache[11] || (_cache[11] = _createElementVNode(\"label\", null, \"产品名称\", -1)),\n            _createVNode(_component_el_input, {\n              modelValue: _ctx.publishForm.productName,\n              \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((_ctx.publishForm.productName) = $event)),\n              placeholder: \"请输入产品名称\",\n              style: {\"width\":\"300px\"}\n            }, null, 8, [\"modelValue\"])\n          ]),\n          _createElementVNode(\"div\", _hoisted_13, [\n            _cache[12] || (_cache[12] = _createElementVNode(\"label\", null, \"版本号\", -1)),\n            _createVNode(_component_el_input, {\n              modelValue: _ctx.publishForm.version,\n              \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((_ctx.publishForm.version) = $event)),\n              placeholder: \"请输入版本号\",\n              style: {\"width\":\"300px\"}\n            }, null, 8, [\"modelValue\"])\n          ]),\n          _createElementVNode(\"div\", _hoisted_14, [\n            _cache[14] || (_cache[14] = _createElementVNode(\"label\", null, \"ZIP 文件\", -1)),\n            _createVNode(_component_el_input, {\n              modelValue: _ctx.publishForm.zipPath,\n              \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((_ctx.publishForm.zipPath) = $event)),\n              placeholder: \"请选择 ZIP 文件\",\n              style: {\"width\":\"300px\"},\n              readonly: \"\"\n            }, null, 8, [\"modelValue\"]),\n            _createVNode(_component_el_button, {\n              onClick: _ctx.selectZipFile,\n              loading: _ctx.selecting\n            }, {\n              default: _withCtx(() => _cache[13] || (_cache[13] = [\n                _createTextVNode(\"选择文件\")\n              ])),\n              _: 1,\n              __: [13]\n            }, 8, [\"onClick\", \"loading\"])\n          ]),\n          _createElementVNode(\"div\", _hoisted_15, [\n            _cache[15] || (_cache[15] = _createElementVNode(\"label\", null, \"更新日志\", -1)),\n            _createVNode(_component_el_input, {\n              modelValue: _ctx.publishForm.changelog,\n              \"onUpdate:modelValue\": _cache[4] || (_cache[4] = ($event: any) => ((_ctx.publishForm.changelog) = $event)),\n              type: \"textarea\",\n              rows: 4,\n              placeholder: \"请输入更新日志（可选）\",\n              style: {\"width\":\"500px\"}\n            }, null, 8, [\"modelValue\"])\n          ]),\n          _createElementVNode(\"div\", _hoisted_16, [\n            _createVNode(_component_el_button, {\n              type: \"primary\",\n              onClick: _ctx.publishVersion,\n              loading: _ctx.publishing,\n              disabled: !_ctx.canPublish\n            }, {\n              default: _withCtx(() => _cache[16] || (_cache[16] = [\n                _createTextVNode(\" 发布版本 \")\n              ])),\n              _: 1,\n              __: [16]\n            }, 8, [\"onClick\", \"loading\", \"disabled\"])\n          ])\n        ])\n      ]),\n      _: 1\n    })\n  ]))\n}", "<template>\n  <div class=\"product-tool\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1>产品工具</h1>\n      <p>管理产品版本发布</p>\n    </div>\n\n    <!-- Cloud 地址配置 -->\n    <el-card class=\"config-card\" shadow=\"never\">\n      <template #header>\n        <div class=\"card-header\">\n          <span>Cloud 配置</span>\n        </div>\n      </template>\n      <div class=\"config-content\">\n        <div class=\"config-row\">\n          <label>Cloud 地址</label>\n          <el-input \n            v-model=\"baseUrl\" \n            placeholder=\"请输入 Cloud 地址\" \n            style=\"width: 400px;\"\n            @change=\"loadProducts\"\n          />\n          <el-button type=\"primary\" @click=\"loadProducts\" :loading=\"loading\">刷新</el-button>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 产品列表 -->\n    <el-card class=\"products-card\" shadow=\"never\">\n      <template #header>\n        <div class=\"card-header\">\n          <span>产品列表</span>\n        </div>\n      </template>\n      <div v-if=\"loading\" class=\"loading-container\">\n        <el-skeleton :rows=\"3\" animated />\n      </div>\n      <div v-else-if=\"products.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无产品数据\" />\n      </div>\n      <div v-else>\n        <el-table :data=\"products\" style=\"width: 100%\" @row-click=\"selectProduct\">\n          <el-table-column prop=\"name\" label=\"产品名称\" width=\"200\" />\n          <el-table-column prop=\"latestVersion\" label=\"最新版本\" width=\"120\" />\n          <el-table-column prop=\"latestChangelog\" label=\"最新更新日志\" show-overflow-tooltip />\n          <el-table-column prop=\"creationTime\" label=\"创建时间\" width=\"180\">\n            <template #default=\"scope\">\n              {{ formatDateTime(scope.row.creationTime) }}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"latestPublishTime\" label=\"最新发布时间\" width=\"180\">\n            <template #default=\"scope\">\n              {{ formatDateTime(scope.row.latestPublishTime) }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"120\">\n            <template #default=\"scope\">\n              <el-button type=\"primary\" size=\"small\" @click.stop=\"selectProduct(scope.row)\">\n                查看版本\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </el-card>\n\n    <!-- 版本列表 -->\n    <el-card v-if=\"selectedProduct\" class=\"versions-card\" shadow=\"never\">\n      <template #header>\n        <div class=\"card-header\">\n          <span>{{ selectedProduct.name }} - 版本列表</span>\n        </div>\n      </template>\n      <div v-if=\"versionsLoading\" class=\"loading-container\">\n        <el-skeleton :rows=\"2\" animated />\n      </div>\n      <div v-else-if=\"versions.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无版本数据\" />\n      </div>\n      <div v-else>\n        <el-table :data=\"versions\" style=\"width: 100%\">\n          <el-table-column prop=\"version\" label=\"版本号\" width=\"120\" />\n          <el-table-column prop=\"changeLog\" label=\"更新日志\" show-overflow-tooltip />\n          <el-table-column prop=\"releaseTime\" label=\"发布时间\" width=\"180\">\n            <template #default=\"scope\">\n              {{ formatDateTime(scope.row.releaseTime) }}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"creationTime\" label=\"创建时间\" width=\"180\">\n            <template #default=\"scope\">\n              {{ formatDateTime(scope.row.creationTime) }}\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n    </el-card>\n\n    <!-- 发布新版本 -->\n    <el-card class=\"publish-card\" shadow=\"never\">\n      <template #header>\n        <div class=\"card-header\">\n          <span>发布新版本</span>\n        </div>\n      </template>\n      <div class=\"publish-content\">\n        <div class=\"form-row\">\n          <label>产品名称</label>\n          <el-input \n            v-model=\"publishForm.productName\" \n            placeholder=\"请输入产品名称\"\n            style=\"width: 300px;\"\n          />\n        </div>\n        <div class=\"form-row\">\n          <label>版本号</label>\n          <el-input \n            v-model=\"publishForm.version\" \n            placeholder=\"请输入版本号\"\n            style=\"width: 300px;\"\n          />\n        </div>\n        <div class=\"form-row\">\n          <label>ZIP 文件</label>\n          <el-input \n            v-model=\"publishForm.zipPath\" \n            placeholder=\"请选择 ZIP 文件\"\n            style=\"width: 300px;\"\n            readonly\n          />\n          <el-button @click=\"selectZipFile\" :loading=\"selecting\">选择文件</el-button>\n        </div>\n        <div class=\"form-row\">\n          <label>更新日志</label>\n          <el-input \n            v-model=\"publishForm.changelog\" \n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入更新日志（可选）\"\n            style=\"width: 500px;\"\n          />\n        </div>\n        <div class=\"form-row\">\n          <el-button \n            type=\"primary\" \n            @click=\"publishVersion\" \n            :loading=\"publishing\"\n            :disabled=\"!canPublish\"\n          >\n            发布版本\n          </el-button>\n        </div>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, computed, onMounted } from \"vue\";\nimport { ElMessage } from \"element-plus\";\nimport { appApi, ProductDto, ProductVersionDto, ProductVersionZipRequest } from \"@/api/appApi\";\n\nexport default defineComponent({\n  name: \"ProductToolView\",\n  setup() {\n    // 数据\n    const baseUrl = ref(\"http://***********:5000/\");\n    const loading = ref(false);\n    const versionsLoading = ref(false);\n    const selecting = ref(false);\n    const publishing = ref(false);\n    \n    const products = ref<ProductDto[]>([]);\n    const selectedProduct = ref<ProductDto | null>(null);\n    const versions = ref<ProductVersionDto[]>([]);\n    \n    const publishForm = ref<ProductVersionZipRequest>({\n      baseUrl: \"\",\n      productName: \"TabKit\",\n      zipPath: \"\",\n      version: \"\",\n      changelog: \"\"\n    });\n\n    // 计算属性\n    const canPublish = computed(() => {\n      return baseUrl.value && \n             publishForm.value.productName && \n             publishForm.value.version && \n             publishForm.value.zipPath;\n    });\n\n    // 方法\n    const loadProducts = async () => {\n      if (!baseUrl.value) {\n        ElMessage.warning(\"请输入 Cloud 地址\");\n        return;\n      }\n\n      loading.value = true;\n      try {\n        const response = await appApi.product.getProducts(baseUrl.value);\n        products.value = response.data;\n        publishForm.value.baseUrl = baseUrl.value;\n      } catch (error) {\n        console.error(\"加载产品列表失败:\", error);\n        ElMessage.error(\"加载产品列表失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    const selectProduct = async (product: ProductDto) => {\n      selectedProduct.value = product;\n      versionsLoading.value = true;\n      \n      try {\n        const response = await appApi.product.getProductVersions(baseUrl.value, product.id);\n        versions.value = response.data;\n      } catch (error) {\n        console.error(\"加载版本列表失败:\", error);\n        ElMessage.error(\"加载版本列表失败\");\n      } finally {\n        versionsLoading.value = false;\n      }\n    };\n\n    const selectZipFile = async () => {\n      selecting.value = true;\n      try {\n        const response = await appApi.product.selectZipFile();\n        publishForm.value.zipPath = response.data;\n      } catch (error) {\n        console.error(\"选择文件失败:\", error);\n        ElMessage.error(\"选择文件失败\");\n      } finally {\n        selecting.value = false;\n      }\n    };\n\n    const publishVersion = async () => {\n      if (!canPublish.value) {\n        ElMessage.warning(\"请填写完整信息\");\n        return;\n      }\n\n      publishing.value = true;\n      try {\n        await appApi.product.publishVersion(publishForm.value);\n        ElMessage.success(\"版本发布成功\");\n        \n        // 重新加载产品列表\n        await loadProducts();\n        \n        // 如果有选中的产品，重新加载版本列表\n        if (selectedProduct.value) {\n          await selectProduct(selectedProduct.value);\n        }\n        \n        // 清空表单\n        publishForm.value.version = \"\";\n        publishForm.value.zipPath = \"\";\n        publishForm.value.changelog = \"\";\n      } catch (error) {\n        console.error(\"发布版本失败:\", error);\n        ElMessage.error(\"发布版本失败\");\n      } finally {\n        publishing.value = false;\n      }\n    };\n\n    const formatDateTime = (dateTime: string | null | undefined) => {\n      if (!dateTime) return \"-\";\n      return new Date(dateTime).toLocaleString(\"zh-CN\");\n    };\n\n    // 生命周期\n    onMounted(() => {\n      publishForm.value.baseUrl = baseUrl.value;\n    });\n\n    return {\n      baseUrl,\n      loading,\n      versionsLoading,\n      selecting,\n      publishing,\n      products,\n      selectedProduct,\n      versions,\n      publishForm,\n      canPublish,\n      loadProducts,\n      selectProduct,\n      selectZipFile,\n      publishVersion,\n      formatDateTime\n    };\n  }\n});\n</script>\n\n<style scoped>\n.product-tool {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: 30px;\n}\n\n.page-header h1 {\n  margin: 0 0 8px 0;\n  font-size: 28px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.page-header p {\n  margin: 0;\n  color: #606266;\n  font-size: 14px;\n}\n\n.config-card,\n.products-card,\n.versions-card,\n.publish-card {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 600;\n}\n\n.config-content {\n  padding: 10px 0;\n}\n\n.config-row {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.config-row label {\n  min-width: 80px;\n  font-weight: 500;\n  color: #606266;\n}\n\n.loading-container {\n  padding: 20px;\n}\n\n.empty-state {\n  padding: 40px 20px;\n  text-align: center;\n}\n\n.form-row {\n  display: flex;\n  align-items: flex-start;\n  gap: 16px;\n  margin-bottom: 20px;\n}\n\n.form-row label {\n  min-width: 80px;\n  padding-top: 8px;\n  font-weight: 500;\n  color: #606266;\n}\n\n.publish-content {\n  padding: 10px 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .product-tool {\n    padding: 10px;\n  }\n\n  .config-row,\n  .form-row {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .config-row label,\n  .form-row label {\n    min-width: auto;\n    padding-top: 0;\n  }\n}\n</style>\n", "import { render } from \"./ProductToolView.vue?vue&type=template&id=99c8b0fe&scoped=true&ts=true\"\nimport script from \"./ProductToolView.vue?vue&type=script&lang=ts\"\nexport * from \"./ProductToolView.vue?vue&type=script&lang=ts\"\n\nimport \"./ProductToolView.vue?vue&type=style&index=0&id=99c8b0fe&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-99c8b0fe\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "key", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_el_input", "_resolveComponent", "_component_el_button", "_component_el_card", "_component_el_skeleton", "_component_el_empty", "_component_el_table_column", "_component_el_table", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "shadow", "header", "_withCtx", "default", "modelValue", "baseUrl", "$event", "placeholder", "style", "onChange", "loadProducts", "type", "onClick", "loading", "_createTextVNode", "_", "__", "rows", "animated", "products", "length", "description", "data", "onRowClick", "selectProduct", "prop", "label", "width", "scope", "_toDisplayString", "formatDateTime", "row", "creationTime", "latestPublishTime", "size", "_withModifiers", "selectedProduct", "_createBlock", "name", "versionsLoading", "versions", "releaseTime", "_createCommentVNode", "publishForm", "productName", "version", "zipPath", "readonly", "selectZipFile", "selecting", "changelog", "publishVersion", "publishing", "disabled", "canPublish", "defineComponent", "setup", "ref", "computed", "value", "async", "response", "appApi", "product", "getProducts", "error", "console", "ElMessage", "warning", "getProductVersions", "id", "success", "dateTime", "Date", "toLocaleString", "onMounted", "__exports__"], "sourceRoot": ""}