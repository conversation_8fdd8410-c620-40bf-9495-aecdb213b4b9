{"version": 3, "file": "js/about.a8b702d6.js", "mappings": "0LAIA,MAAMA,EAAa,CCHZC,MAAM,SDIPC,EAAa,CCSVD,MAAM,kBDRTE,EAAa,CCWJF,MAAM,eDVfG,EAAa,CCgBNH,MAAM,gBDfbI,EAAa,CCmBJJ,MAAM,mBDlBfK,EAAa,CCoBFL,MAAM,mBDnBjBM,EAAa,CCwBAN,MAAM,gBDvBnBO,EAAa,CCoCJP,MAAM,eDlCf,SAAUQ,EAAOC,EAAUC,EAAYC,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAA+BC,EAAAA,EAAAA,IAAkB,qBACjDC,GAAqBD,EAAAA,EAAAA,IAAkB,WAE7C,OAAQE,EAAAA,EAAAA,OChBRC,EAAAA,EAAAA,IAkFM,MAlFNpB,EAkFM,CDjEJW,EAAO,KAAOA,EAAO,IAAKU,EAAAA,EAAAA,IAAmB,oGAA2GC,EAAa,+LAAyM,KCJ9WC,EAAAA,EAAAA,IAoEM,MApENrB,EAoEM,EAnEJsB,EAAAA,EAAAA,IA2BUN,EAAA,CA3BDjB,MAAM,aAAW,CACbwB,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNH,EAAAA,EAAAA,IAGM,MAHNpB,EAGM,EAFJqB,EAAAA,EAAAA,IAAwCR,EAAA,CAArBW,KAAK,gBDMxBhB,EAAO,KAAOA,EAAO,ICLrBY,EAAAA,EAAAA,IAAsB,YAAhB,aAAS,QDQnBK,SAASF,EAAAA,EAAAA,ICJT,IAkBM,EAlBNH,EAAAA,EAAAA,IAkBM,MAlBNnB,EAkBM,CDZFO,EAAO,KAAOA,EAAO,ICLvBY,EAAAA,EAAAA,IAEI,KAFDtB,MAAM,eAAc,oDAEvB,KACAsB,EAAAA,EAAAA,IAYM,MAZNlB,EAYM,CDRFM,EAAO,KAAOA,EAAO,ICHvBY,EAAAA,EAAAA,IAAkC,YAA5B,yBAAqB,KAC3BA,EAAAA,EAAAA,IASM,MATNjB,EASM,EARJiB,EAAAA,EAAAA,IAGM,OAHDtB,MAAM,eAAgB4B,QAAKlB,EAAA,KAAAA,EAAA,GDO9C,IAAImB,ICP4CpB,EAAAqB,WAAArB,EAAAqB,aAAAD,KDQ7B,ECPHN,EAAAA,EAAAA,IAA6DR,EAAA,CAA1CW,KAAK,cAAc1B,MAAM,iBDY1CU,EAAO,KAAOA,EAAO,ICXvBY,EAAAA,EAAAA,IAAoD,QAA9CtB,MAAM,gBAAe,sBAAkB,OAE/CsB,EAAAA,EAAAA,IAGM,MAHNhB,EAGM,EAFJiB,EAAAA,EAAAA,IAA0DR,EAAA,CAAvCW,KAAK,WAAW1B,MAAM,iBDevCU,EAAO,KAAOA,EAAO,ICdvBY,EAAAA,EAAAA,IAAuF,KAApFS,KAAK,gCAAgC/B,MAAM,gBAAe,0BAAsB,cDuB3FgC,EAAG,KCdLT,EAAAA,EAAAA,IAoCUN,EAAA,CApCDjB,MAAM,aAAW,CACbwB,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNH,EAAAA,EAAAA,IAGM,MAHNf,EAGM,EAFJgB,EAAAA,EAAAA,IAAiCR,EAAA,CAAdW,KAAK,SDiBxBhB,EAAO,KAAOA,EAAO,IChBrBY,EAAAA,EAAAA,IAAiB,YAAX,QAAI,QDmBdK,SAASF,EAAAA,EAAAA,ICfT,IA2BM,CDXJf,EAAO,KAAOA,EAAO,IChBvBY,EAAAA,EAAAA,IA2BM,OA3BDtB,MAAM,aAAW,EACpBsB,EAAAA,EAAAA,IAyBM,OAzBDtB,MAAM,aAAW,EACpBsB,EAAAA,EAAAA,IAGM,OAHDtB,MAAM,aAAW,EACpBsB,EAAAA,EAAAA,IAAqC,QAA/BtB,MAAM,cAAa,UACzBsB,EAAAA,EAAAA,IAAkD,QAA5CtB,MAAM,cAAa,yBAE3BsB,EAAAA,EAAAA,IAGM,OAHDtB,MAAM,aAAW,EACpBsB,EAAAA,EAAAA,IAAsC,QAAhCtB,MAAM,cAAa,WACzBsB,EAAAA,EAAAA,IAA4C,QAAtCtB,MAAM,cAAa,mBAE3BsB,EAAAA,EAAAA,IAGM,OAHDtB,MAAM,aAAW,EACpBsB,EAAAA,EAAAA,IAAoC,QAA9BtB,MAAM,cAAa,SACzBsB,EAAAA,EAAAA,IAA2C,QAArCtB,MAAM,cAAa,kBAE3BsB,EAAAA,EAAAA,IAGM,OAHDtB,MAAM,aAAW,EACpBsB,EAAAA,EAAAA,IAAqC,QAA/BtB,MAAM,cAAa,UACzBsB,EAAAA,EAAAA,IAA8C,QAAxCtB,MAAM,cAAa,qBAE3BsB,EAAAA,EAAAA,IAGM,OAHDtB,MAAM,aAAW,EACpBsB,EAAAA,EAAAA,IAAqC,QAA/BtB,MAAM,cAAa,UACzBsB,EAAAA,EAAAA,IAA8C,QAAxCtB,MAAM,cAAa,qBAE3BsB,EAAAA,EAAAA,IAGM,OAHDtB,MAAM,aAAW,EACpBsB,EAAAA,EAAAA,IAAqC,QAA/BtB,MAAM,cAAa,UACzBsB,EAAAA,EAAAA,IAAiD,QAA3CtB,MAAM,cAAa,2BDmBzB,MAENgC,EAAG,EACHC,GAAI,CAAC,QAIb,C,uBCVA,GAAeC,EAAAA,EAAAA,IAAgB,CAC7BC,KAAM,YACNC,WAAY,CACVC,gBAAeA,EAAAA,IAEjBC,KAAAA,GACE,MAAMR,EAAYS,UAChB,MAAMC,EAAO,4EACPC,EAAAA,GAAOC,SAASC,aAAaH,IAGrC,MAAO,CACLV,YAEJ,I,UCpGF,MAAMc,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASpC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://tab-kit-web/./src/views/AboutView.vue?6c20", "webpack://tab-kit-web/./src/views/AboutView.vue", "webpack://tab-kit-web/./src/views/AboutView.vue?d56f"], "sourcesContent": ["import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createStaticVNode as _createStaticVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\nimport _imports_0 from '@/assets/logo.svg'\n\n\nconst _hoisted_1 = { class: \"about\" }\nconst _hoisted_2 = { class: \"app-info-cards\" }\nconst _hoisted_3 = { class: \"card-header\" }\nconst _hoisted_4 = { class: \"info-content\" }\nconst _hoisted_5 = { class: \"contact-section\" }\nconst _hoisted_6 = { class: \"contact-methods\" }\nconst _hoisted_7 = { class: \"contact-item\" }\nconst _hoisted_8 = { class: \"card-header\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _cache[8] || (_cache[8] = _createStaticVNode(\"<div class=\\\"page-header\\\" data-v-8bf9f630><div class=\\\"app-logo-section\\\" data-v-8bf9f630><img src=\\\"\" + _imports_0 + \"\\\" alt=\\\"TabKit Logo\\\" class=\\\"logo-icon\\\" data-v-8bf9f630><div class=\\\"app-info\\\" data-v-8bf9f630><h1 data-v-8bf9f630>TabKit</h1><p class=\\\"version\\\" data-v-8bf9f630>版本 1.0.7</p></div></div></div>\", 1)),\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createVNode(_component_el_card, { class: \"info-card\" }, {\n        header: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_3, [\n            _createVNode(_component_font_awesome_icon, { icon: \"info-circle\" }),\n            _cache[1] || (_cache[1] = _createElementVNode(\"span\", null, \"关于 TabKit\", -1))\n          ])\n        ]),\n        default: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_4, [\n            _cache[5] || (_cache[5] = _createElementVNode(\"p\", { class: \"description\" }, \" Alsi.Tab.Kit 是一个集成多种工具的桌面应用程序，旨在提高工作效率和简化日常任务。 \", -1)),\n            _createElementVNode(\"div\", _hoisted_5, [\n              _cache[4] || (_cache[4] = _createElementVNode(\"span\", null, \"使用过程中遇到问题或有任何建议，欢迎提出：\", -1)),\n              _createElementVNode(\"div\", _hoisted_6, [\n                _createElementVNode(\"div\", {\n                  class: \"contact-item\",\n                  onClick: _cache[0] || (_cache[0] = \n//@ts-ignore\n(...args) => (_ctx.openTeams && _ctx.openTeams(...args)))\n                }, [\n                  _createVNode(_component_font_awesome_icon, {\n                    icon: \"comment-alt\",\n                    class: \"contact-icon\"\n                  }),\n                  _cache[2] || (_cache[2] = _createElementVNode(\"span\", { class: \"contact-link\" }, \"Feedback via Teams\", -1))\n                ]),\n                _createElementVNode(\"div\", _hoisted_7, [\n                  _createVNode(_component_font_awesome_icon, {\n                    icon: \"envelope\",\n                    class: \"contact-icon\"\n                  }),\n                  _cache[3] || (_cache[3] = _createElementVNode(\"a\", {\n                    href: \"mailto:<EMAIL>\",\n                    class: \"contact-link\"\n                  }, \"<EMAIL>\", -1))\n                ])\n              ])\n            ])\n          ])\n        ]),\n        _: 1\n      }),\n      _createVNode(_component_el_card, { class: \"info-card\" }, {\n        header: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_8, [\n            _createVNode(_component_font_awesome_icon, { icon: \"code\" }),\n            _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \"技术信息\", -1))\n          ])\n        ]),\n        default: _withCtx(() => [\n          _cache[7] || (_cache[7] = _createElementVNode(\"div\", { class: \"tech-info\" }, [\n            _createElementVNode(\"div\", { class: \"tech-grid\" }, [\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"前端框架:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"Vue 3 + TypeScript\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"UI组件库:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"Element Plus\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"图标库:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"FontAwesome\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"后端框架:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \".NET Framework\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"桌面框架:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"WPF + WebView2\")\n              ]),\n              _createElementVNode(\"div\", { class: \"tech-item\" }, [\n                _createElementVNode(\"span\", { class: \"tech-label\" }, \"构建工具:\"),\n                _createElementVNode(\"span\", { class: \"tech-value\" }, \"Vue CLI + Webpack\")\n              ])\n            ])\n          ], -1))\n        ]),\n        _: 1,\n        __: [7]\n      })\n    ])\n  ]))\n}", "<template>\n  <div class=\"about\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <div class=\"app-logo-section\">\n        <img src=\"@/assets/logo.svg\" alt=\"TabKit Logo\" class=\"logo-icon\" />\n        <div class=\"app-info\">\n          <h1>TabKit</h1>\n          <p class=\"version\">版本 1.0.7</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- 应用信息 -->\n    <div class=\"app-info-cards\">\n      <el-card class=\"info-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <font-awesome-icon icon=\"info-circle\" />\n            <span>关于 TabKit</span>\n          </div>\n        </template>\n\n        <div class=\"info-content\">\n          <p class=\"description\">\n            Alsi.Tab.Kit 是一个集成多种工具的桌面应用程序，旨在提高工作效率和简化日常任务。\n          </p>\n          <div class=\"contact-section\">\n            <span>使用过程中遇到问题或有任何建议，欢迎提出：</span>\n            <div class=\"contact-methods\">\n              <div class=\"contact-item\" @click=\"openTeams\">\n                <font-awesome-icon icon=\"comment-alt\" class=\"contact-icon\" />\n                <span class=\"contact-link\">Feedback via Teams</span>\n              </div>\n              <div class=\"contact-item\">\n                <font-awesome-icon icon=\"envelope\" class=\"contact-icon\" />\n                <a href=\"mailto:<EMAIL>\" class=\"contact-link\"><EMAIL></a>\n              </div>\n            </div>\n          </div>\n\n        </div>\n      </el-card>\n\n      <!-- 技术信息 -->\n      <el-card class=\"info-card\">\n        <template #header>\n          <div class=\"card-header\">\n            <font-awesome-icon icon=\"code\" />\n            <span>技术信息</span>\n          </div>\n        </template>\n\n        <div class=\"tech-info\">\n          <div class=\"tech-grid\">\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">前端框架:</span>\n              <span class=\"tech-value\">Vue 3 + TypeScript</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">UI组件库:</span>\n              <span class=\"tech-value\">Element Plus</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">图标库:</span>\n              <span class=\"tech-value\">FontAwesome</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">后端框架:</span>\n              <span class=\"tech-value\">.NET Framework</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">桌面框架:</span>\n              <span class=\"tech-value\">WPF + WebView2</span>\n            </div>\n            <div class=\"tech-item\">\n              <span class=\"tech-label\">构建工具:</span>\n              <span class=\"tech-value\">Vue CLI + Webpack</span>\n            </div>\n          </div>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent } from \"vue\";\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';\nimport {\n  appApi\n} from \"@/api/appApi\";\n\nexport default defineComponent({\n  name: \"AboutView\",\n  components: {\n    FontAwesomeIcon,\n  },\n  setup() {\n    const openTeams = async () => {\n      const path = 'https://teams.microsoft.com/l/chat/0/0?users=<EMAIL>';\n      await appApi.explorer.startProcess(path);\n    };\n\n    return {\n      openTeams\n    };\n  }\n});\n</script>\n\n<style scoped>\n.about {\n  padding: 20px;\n  max-width: 1000px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: 40px;\n}\n\n.app-logo-section {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  justify-content: center;\n}\n\n.logo-icon {\n  width: 4rem;\n  height: 4rem;\n  flex-shrink: 0;\n}\n\n.app-info {\n  text-align: left;\n}\n\n.app-info h1 {\n  font-size: 2.5rem;\n  color: #2c3e50;\n  margin: 0;\n}\n\n.version {\n  color: #7f8c8d;\n  font-size: 1.1rem;\n  margin: 0;\n}\n\n.app-info-cards {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.info-card {\n  border-radius: 8px;\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: bold;\n  color: #2c3e50;\n  font-size: 1.1rem;\n}\n\n.info-content {\n  line-height: 1.6;\n}\n\n.description {\n  color: #555;\n  margin-bottom: 10px;\n}\n\n.features h3 {\n  color: #2c3e50;\n  margin-bottom: 15px;\n  font-size: 1.2rem;\n}\n\n.features ul {\n  list-style: none;\n  padding: 0;\n}\n\n.features li {\n  display: flex;\n  align-items: flex-start;\n  gap: 10px;\n  margin-bottom: 15px;\n  padding: 10px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n}\n\n.feature-icon {\n  color: #3498db;\n  margin-top: 2px;\n  flex-shrink: 0;\n}\n\n.features li strong {\n  color: #2c3e50;\n}\n\n.tech-info {\n  padding: 10px 0;\n}\n\n.tech-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 15px;\n}\n\n.tech-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px;\n  background-color: #f8f9fa;\n  border-radius: 6px;\n}\n\n.tech-label {\n  color: #7f8c8d;\n  font-weight: 500;\n}\n\n.tech-value {\n  color: #2c3e50;\n  font-weight: bold;\n}\n\n.contact-section {\n  margin-top: 15px;\n}\n\n.contact-methods {\n  display: flex;\n  gap: 40px;\n  margin-top: 10px;\n  flex-wrap: wrap;\n}\n\n.contact-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.contact-icon {\n  color: var(--el-color-primary);\n  width: 16px;\n  flex-shrink: 0;\n}\n\n.contact-link {\n  color: var(--el-color-primary);\n  text-decoration: none;\n  transition: color 0.3s ease;\n  font-weight: 500;\n  cursor: pointer;\n}\n\n.contact-link:hover {\n  color: var(--el-color-primary-dark-1);\n  text-decoration: underline;\n}\n\n\n\n@media (max-width: 768px) {\n  .about {\n    padding: 15px;\n  }\n\n  .app-logo-section {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .app-info {\n    text-align: center;\n  }\n\n  .app-info h1 {\n    font-size: 2rem;\n  }\n\n  .logo-icon {\n    width: 3rem;\n    height: 3rem;\n  }\n\n  .tech-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .tech-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n\n  .features li {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .contact-methods {\n    flex-direction: column;\n    gap: 12px;\n  }\n}\n</style>\n\r\n\r\n", "import { render } from \"./AboutView.vue?vue&type=template&id=8bf9f630&scoped=true&ts=true\"\nimport script from \"./AboutView.vue?vue&type=script&lang=ts\"\nexport * from \"./AboutView.vue?vue&type=script&lang=ts\"\n\nimport \"./AboutView.vue?vue&type=style&index=0&id=8bf9f630&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-8bf9f630\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "_component_font_awesome_icon", "_resolveComponent", "_component_el_card", "_openBlock", "_createElementBlock", "_createStaticVNode", "_imports_0", "_createElementVNode", "_createVNode", "header", "_withCtx", "icon", "default", "onClick", "args", "openTeams", "href", "_", "__", "defineComponent", "name", "components", "FontAwesomeIcon", "setup", "async", "path", "appApi", "explorer", "startProcess", "__exports__"], "sourceRoot": ""}