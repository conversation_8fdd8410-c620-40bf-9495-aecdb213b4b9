﻿using Alsi.Common.Utils;
using Alsi.Tab.Cloud.Proxy;
using System;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Alsi.Tab.Kit.Core.Services.Product
{
    public class ProductService
    {
        public async Task<ProductDto[]> GetProductsAsync(string baseUrl)
        {
            using (var httpClient = new HttpClient())
            {
                var productClient = new ProductClient(httpClient);
                productClient.BaseUrl = baseUrl;
                var products = await productClient.GetAsync();
                return products.ToArray();
            }
        }

        public async Task<ProductVersionDto[]> GetProductVersionsAsync(string baseUrl, Guid productId)
        {
            using (var httpClient = new HttpClient())
            {
                var productVersionClient = new ProductVersionClient(httpClient);
                productVersionClient.BaseUrl = baseUrl;
                var productVersions = await productVersionClient.GetAsync(productId);
                return productVersions.ToArray();
            }
        }

        public async Task PublishProductVersionZipAsync(ProductVersionZipRequest request)
        {
            using (var httpClient = new HttpClient())
            {
                var baseUrl = request.BaseUrl;
                var productName = request.ProductName;
                var zipPath = request.ZipPath;
                var version = request.Version;
                var changelog = request.Changelog;
                var releaseTime = DateTime.Now;

                var productClient = new ProductClient(httpClient);
                productClient.BaseUrl = baseUrl;

                // 判断产品是否存在，如果不存在，创建产品
                var products = await productClient.GetAsync();
                var product = products.FirstOrDefault(x => x.Name == productName);
                if (product == null)
                {
                    product = await productClient.PostAsync(productName);
                }

                // 上传 zip 安装包
                var fileClient = new FileClient(httpClient);
                fileClient.BaseUrl = baseUrl;

                var zipName = Path.GetFileName(zipPath);
                var hash = HashUtils.CalculateHash(zipPath);
                var fileParameter = new FileParameter(File.OpenRead(zipPath), zipName);
                var zipFileEntry = await fileClient.UploadAsync(hash, 1, 1, false, fileParameter);

                var productVersionClient = new ProductVersionClient(httpClient);
                productVersionClient.BaseUrl = baseUrl;

                // 创建产品版本
                var productVersion = await productVersionClient.PostAsync(
                    product.Id, version, changelog, null, releaseTime);

                // 设置产品版本和 zip 安装包的关联
                await productVersionClient.SetProductVersionZipFileIdAsync(productVersion.Id, zipFileEntry.Id);
            }
        }
    }
}
