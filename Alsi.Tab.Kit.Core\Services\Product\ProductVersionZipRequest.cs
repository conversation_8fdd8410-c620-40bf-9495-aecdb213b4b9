﻿using System;
using System.IO;

namespace Alsi.Tab.Kit.Core.Services.Product
{
    public class ProductVersionZipRequest
    {
        public string BaseUrl { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string ZipPath { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Changelog { get; set; } = string.Empty;

        public void Validate()
        {
            if (string.IsNullOrWhiteSpace(BaseUrl))
            {
                throw new ArgumentException("BaseUrl 不能为空", nameof(BaseUrl));
            }

            if (!Uri.TryCreate(BaseUrl, UriKind.Absolute, out _))
            {
                throw new ArgumentException("BaseUrl 格式不正确", nameof(BaseUrl));
            }

            if (string.IsNullOrWhiteSpace(ProductName))
            {
                throw new ArgumentException("ProductName 不能为空", nameof(ProductName));
            }

            if (string.IsNullOrWhiteSpace(ZipPath))
            {
                throw new ArgumentException("ZipPath 不能为空", nameof(ZipPath));
            }

            if (!File.Exists(ZipPath))
            {
                throw new FileNotFoundException($"指定的 ZIP 文件不存在: {ZipPath}", ZipPath);
            }

            if (!ZipPath.EndsWith(".zip", StringComparison.OrdinalIgnoreCase))
            {
                throw new ArgumentException("ZipPath 必须是 .zip 文件", nameof(ZipPath));
            }

            if (string.IsNullOrWhiteSpace(Version))
            {
                throw new ArgumentException("Version 不能为空", nameof(Version));
            }

            // Changelog 可以为空，不需要验证
        }
    }
}
