﻿namespace Alsi.Tab.Kit.Core.Services.Product
{
    public class ProductVersionZipRequest
    {
        public string BaseUrl { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string ZipPath { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Changelog { get; set; } = string.Empty;

        public void Validate()
        {
        }
    }
}
