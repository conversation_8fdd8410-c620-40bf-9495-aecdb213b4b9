{"Version": 1, "WorkspaceRootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{D7D76682-65BB-461C-B1E9-AF038AAE0D22}|Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.web\\dto\\productdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D7D76682-65BB-461C-B1E9-AF038AAE0D22}|Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj|solutionrelative:alsi.tab.kit.web\\dto\\productdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D7D76682-65BB-461C-B1E9-AF038AAE0D22}|Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.web\\controllers\\productcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D7D76682-65BB-461C-B1E9-AF038AAE0D22}|Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj|solutionrelative:alsi.tab.kit.web\\controllers\\productcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\alsi.tab.kit.core\\services\\product\\productservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\product\\productservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2F91AF70-A4DD-B37E-C2DF-F3DD2AF30239}|..\\Alsi.Tab.Cloud\\Alsi.Tab.Cloud.Proxy\\Alsi.Tab.Cloud.Proxy.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.cloud\\alsi.tab.cloud.proxy\\generatedproxycode.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2F91AF70-A4DD-B37E-C2DF-F3DD2AF30239}|..\\Alsi.Tab.Cloud\\Alsi.Tab.Cloud.Proxy\\Alsi.Tab.Cloud.Proxy.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.cloud\\alsi.tab.cloud.proxy\\manifest\\productmanifest.fileentry.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\alsi.tab.kit.core\\services\\product\\productversionziprequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\product\\productversionziprequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "ProductDto.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Dto\\ProductDto.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Web\\Dto\\ProductDto.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Dto\\ProductDto.cs", "RelativeToolTip": "Alsi.Tab.Kit.Web\\Dto\\ProductDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T04:50:32.564Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ProductController.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Controllers\\ProductController.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Web\\Controllers\\ProductController.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Controllers\\ProductController.cs", "RelativeToolTip": "Alsi.Tab.Kit.Web\\Controllers\\ProductController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T04:48:06.412Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "GeneratedProxyCode.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Cloud\\Alsi.Tab.Cloud.Proxy\\GeneratedProxyCode.cs", "RelativeDocumentMoniker": "..\\Alsi.Tab.Cloud\\Alsi.Tab.Cloud.Proxy\\GeneratedProxyCode.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Cloud\\Alsi.Tab.Cloud.Proxy\\GeneratedProxyCode.cs", "RelativeToolTip": "..\\Alsi.Tab.Cloud\\Alsi.Tab.Cloud.Proxy\\GeneratedProxyCode.cs", "ViewState": "AgIAAB8cAAAAAAAAAAAIwC4cAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T03:46:05.133Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ProductManifest.FileEntry.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Cloud\\Alsi.Tab.Cloud.Proxy\\Manifest\\ProductManifest.FileEntry.cs", "RelativeDocumentMoniker": "..\\Alsi.Tab.Cloud\\Alsi.Tab.Cloud.Proxy\\Manifest\\ProductManifest.FileEntry.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Cloud\\Alsi.Tab.Cloud.Proxy\\Manifest\\ProductManifest.FileEntry.cs", "RelativeToolTip": "..\\Alsi.Tab.Cloud\\Alsi.Tab.Cloud.Proxy\\Manifest\\ProductManifest.FileEntry.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T04:35:08.706Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ProductVersionZipRequest.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Product\\ProductVersionZipRequest.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\Product\\ProductVersionZipRequest.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Product\\ProductVersionZipRequest.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\Product\\ProductVersionZipRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T04:31:28.847Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "ProductService.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Product\\ProductService.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\Product\\ProductService.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Product\\ProductService.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\Product\\ProductService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAswAwAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-11T03:34:57.221Z", "EditorCaption": ""}]}]}]}