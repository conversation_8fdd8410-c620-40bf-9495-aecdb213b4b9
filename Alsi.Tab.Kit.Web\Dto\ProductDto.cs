using System;

namespace Alsi.Tab.Kit.Web.Dto
{
    public class ProductDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTimeOffset CreationTime { get; set; }
        public DateTimeOffset? LastModificationTime { get; set; }
        public string LatestVersion { get; set; } = string.Empty;
        public string LatestChangelog { get; set; } = string.Empty;
        public DateTimeOffset? LatestPublishTime { get; set; }
    }

    public class ProductVersionDto
    {
        public Guid Id { get; set; }
        public Guid ProductId { get; set; }
        public string Version { get; set; } = string.Empty;
        public string ChangeLog { get; set; } = string.Empty;
        public Guid? ManifestFileId { get; set; }
        public Guid? InstallerFileId { get; set; }
        public Guid? ZipFileId { get; set; }
        public DateTimeOffset ReleaseTime { get; set; }
        public DateTimeOffset CreationTime { get; set; }
        public DateTimeOffset? LastModificationTime { get; set; }
    }

    public class ProductVersionZipRequestDto
    {
        public string BaseUrl { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string ZipPath { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Changelog { get; set; } = string.Empty;
    }
}
