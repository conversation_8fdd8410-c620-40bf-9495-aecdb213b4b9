"use strict";(self["webpackChunktab_kit_web"]=self["webpackChunktab_kit_web"]||[]).push([[575],{4703:function(e,l,a){a.r(l),a.d(l,{default:function(){return R}});var o=a(6768),t=a(4232),r=a(5130);const s={class:"product-tool"},i={class:"config-content"},d={class:"config-row"},n={key:0,class:"loading-container"},u={key:1,class:"empty-state"},c={key:2},p={class:"card-header"},b={key:0,class:"loading-container"},h={key:1,class:"empty-state"},v={key:2},k={class:"publish-content"},m={class:"form-row"},w={class:"form-row"},g={class:"form-row"},f={class:"form-row"},F={class:"form-row"};function y(e,l,a,y,_,L){const C=(0,o.g2)("el-input"),P=(0,o.g2)("el-button"),V=(0,o.g2)("el-card"),T=(0,o.g2)("el-skeleton"),R=(0,o.g2)("el-empty"),U=(0,o.g2)("el-table-column"),K=(0,o.g2)("el-table");return(0,o.uX)(),(0,o.CE)("div",s,[l[17]||(l[17]=(0,o.Lk)("div",{class:"page-header"},[(0,o.Lk)("h1",null,"产品工具"),(0,o.Lk)("p",null,"管理产品版本发布")],-1)),(0,o.bF)(V,{class:"config-card",shadow:"never"},{header:(0,o.k6)(()=>l[5]||(l[5]=[(0,o.Lk)("div",{class:"card-header"},[(0,o.Lk)("span",null,"Cloud 配置")],-1)])),default:(0,o.k6)(()=>[(0,o.Lk)("div",i,[(0,o.Lk)("div",d,[l[7]||(l[7]=(0,o.Lk)("label",null,"Cloud 地址",-1)),(0,o.bF)(C,{modelValue:e.baseUrl,"onUpdate:modelValue":l[0]||(l[0]=l=>e.baseUrl=l),placeholder:"请输入 Cloud 地址",style:{width:"400px"},onChange:e.loadProducts},null,8,["modelValue","onChange"]),(0,o.bF)(P,{type:"primary",onClick:e.loadProducts,loading:e.loading},{default:(0,o.k6)(()=>l[6]||(l[6]=[(0,o.eW)("刷新")])),_:1,__:[6]},8,["onClick","loading"])])])]),_:1}),(0,o.bF)(V,{class:"products-card",shadow:"never"},{header:(0,o.k6)(()=>l[8]||(l[8]=[(0,o.Lk)("div",{class:"card-header"},[(0,o.Lk)("span",null,"产品列表")],-1)])),default:(0,o.k6)(()=>[e.loading?((0,o.uX)(),(0,o.CE)("div",n,[(0,o.bF)(T,{rows:3,animated:""})])):0===e.products.length?((0,o.uX)(),(0,o.CE)("div",u,[(0,o.bF)(R,{description:"暂无产品数据"})])):((0,o.uX)(),(0,o.CE)("div",c,[(0,o.bF)(K,{data:e.products,style:{width:"100%"},onRowClick:e.selectProduct},{default:(0,o.k6)(()=>[(0,o.bF)(U,{prop:"name",label:"产品名称",width:"200"}),(0,o.bF)(U,{prop:"latestVersion",label:"最新版本",width:"120"}),(0,o.bF)(U,{prop:"latestChangelog",label:"最新更新日志","show-overflow-tooltip":""}),(0,o.bF)(U,{prop:"creationTime",label:"创建时间",width:"180"},{default:(0,o.k6)(l=>[(0,o.eW)((0,t.v_)(e.formatDateTime(l.row.creationTime)),1)]),_:1}),(0,o.bF)(U,{prop:"latestPublishTime",label:"最新发布时间",width:"180"},{default:(0,o.k6)(l=>[(0,o.eW)((0,t.v_)(e.formatDateTime(l.row.latestPublishTime)),1)]),_:1}),(0,o.bF)(U,{label:"操作",width:"120"},{default:(0,o.k6)(a=>[(0,o.bF)(P,{type:"primary",size:"small",onClick:(0,r.D$)(l=>e.selectProduct(a.row),["stop"])},{default:(0,o.k6)(()=>l[9]||(l[9]=[(0,o.eW)(" 查看版本 ")])),_:2,__:[9]},1032,["onClick"])]),_:1})]),_:1},8,["data","onRowClick"])]))]),_:1}),e.selectedProduct?((0,o.uX)(),(0,o.Wv)(V,{key:0,class:"versions-card",shadow:"never"},{header:(0,o.k6)(()=>[(0,o.Lk)("div",p,[(0,o.Lk)("span",null,(0,t.v_)(e.selectedProduct.name)+" - 版本列表",1)])]),default:(0,o.k6)(()=>[e.versionsLoading?((0,o.uX)(),(0,o.CE)("div",b,[(0,o.bF)(T,{rows:2,animated:""})])):0===e.versions.length?((0,o.uX)(),(0,o.CE)("div",h,[(0,o.bF)(R,{description:"暂无版本数据"})])):((0,o.uX)(),(0,o.CE)("div",v,[(0,o.bF)(K,{data:e.versions,style:{width:"100%"}},{default:(0,o.k6)(()=>[(0,o.bF)(U,{prop:"version",label:"版本号",width:"120"}),(0,o.bF)(U,{prop:"changeLog",label:"更新日志","show-overflow-tooltip":""}),(0,o.bF)(U,{prop:"releaseTime",label:"发布时间",width:"180"},{default:(0,o.k6)(l=>[(0,o.eW)((0,t.v_)(e.formatDateTime(l.row.releaseTime)),1)]),_:1}),(0,o.bF)(U,{prop:"creationTime",label:"创建时间",width:"180"},{default:(0,o.k6)(l=>[(0,o.eW)((0,t.v_)(e.formatDateTime(l.row.creationTime)),1)]),_:1})]),_:1},8,["data"])]))]),_:1})):(0,o.Q3)("",!0),(0,o.bF)(V,{class:"publish-card",shadow:"never"},{header:(0,o.k6)(()=>l[10]||(l[10]=[(0,o.Lk)("div",{class:"card-header"},[(0,o.Lk)("span",null,"发布新版本")],-1)])),default:(0,o.k6)(()=>[(0,o.Lk)("div",k,[(0,o.Lk)("div",m,[l[11]||(l[11]=(0,o.Lk)("label",null,"产品名称",-1)),(0,o.bF)(C,{modelValue:e.publishForm.productName,"onUpdate:modelValue":l[1]||(l[1]=l=>e.publishForm.productName=l),placeholder:"请输入产品名称",style:{width:"300px"}},null,8,["modelValue"])]),(0,o.Lk)("div",w,[l[12]||(l[12]=(0,o.Lk)("label",null,"版本号",-1)),(0,o.bF)(C,{modelValue:e.publishForm.version,"onUpdate:modelValue":l[2]||(l[2]=l=>e.publishForm.version=l),placeholder:"请输入版本号",style:{width:"300px"}},null,8,["modelValue"])]),(0,o.Lk)("div",g,[l[14]||(l[14]=(0,o.Lk)("label",null,"ZIP 文件",-1)),(0,o.bF)(C,{modelValue:e.publishForm.zipPath,"onUpdate:modelValue":l[3]||(l[3]=l=>e.publishForm.zipPath=l),placeholder:"请选择 ZIP 文件",style:{width:"300px"},readonly:""},null,8,["modelValue"]),(0,o.bF)(P,{onClick:e.selectZipFile,loading:e.selecting},{default:(0,o.k6)(()=>l[13]||(l[13]=[(0,o.eW)("选择文件")])),_:1,__:[13]},8,["onClick","loading"])]),(0,o.Lk)("div",f,[l[15]||(l[15]=(0,o.Lk)("label",null,"更新日志",-1)),(0,o.bF)(C,{modelValue:e.publishForm.changelog,"onUpdate:modelValue":l[4]||(l[4]=l=>e.publishForm.changelog=l),type:"textarea",rows:4,placeholder:"请输入更新日志（可选）",style:{width:"500px"}},null,8,["modelValue"])]),(0,o.Lk)("div",F,[(0,o.bF)(P,{type:"primary",onClick:e.publishVersion,loading:e.publishing,disabled:!e.canPublish},{default:(0,o.k6)(()=>l[16]||(l[16]=[(0,o.eW)(" 发布版本 ")])),_:1,__:[16]},8,["onClick","loading","disabled"])])])]),_:1})])}var _=a(144),L=a(1219),C=a(1021),P=(0,o.pM)({name:"ProductToolView",setup(){const e=(0,_.KR)("http://***********:5000/"),l=(0,_.KR)(!1),a=(0,_.KR)(!1),t=(0,_.KR)(!1),r=(0,_.KR)(!1),s=(0,_.KR)([]),i=(0,_.KR)(null),d=(0,_.KR)([]),n=(0,_.KR)({baseUrl:"",productName:"TabKit",zipPath:"",version:"",changelog:""}),u=(0,o.EW)(()=>e.value&&n.value.productName&&n.value.version&&n.value.zipPath),c=async()=>{if(e.value){l.value=!0;try{const l=await C.GQ.product.getProducts(e.value);s.value=l.data,n.value.baseUrl=e.value}catch(a){console.error("加载产品列表失败:",a),L.nk.error("加载产品列表失败")}finally{l.value=!1}}else L.nk.warning("请输入 Cloud 地址")},p=async l=>{i.value=l,a.value=!0;try{const a=await C.GQ.product.getProductVersions(e.value,l.id);d.value=a.data}catch(o){console.error("加载版本列表失败:",o),L.nk.error("加载版本列表失败")}finally{a.value=!1}},b=async()=>{t.value=!0;try{const e=await C.GQ.product.selectZipFile();n.value.zipPath=e.data}catch(e){console.error("选择文件失败:",e),L.nk.error("选择文件失败")}finally{t.value=!1}},h=async()=>{if(u.value){r.value=!0;try{await C.GQ.product.publishVersion(n.value),L.nk.success("版本发布成功"),await c(),i.value&&await p(i.value),n.value.version="",n.value.zipPath="",n.value.changelog=""}catch(e){console.error("发布版本失败:",e),L.nk.error("发布版本失败")}finally{r.value=!1}}else L.nk.warning("请填写完整信息")},v=e=>e?new Date(e).toLocaleString("zh-CN"):"-";return(0,o.sV)(()=>{n.value.baseUrl=e.value}),{baseUrl:e,loading:l,versionsLoading:a,selecting:t,publishing:r,products:s,selectedProduct:i,versions:d,publishForm:n,canPublish:u,loadProducts:c,selectProduct:p,selectZipFile:b,publishVersion:h,formatDateTime:v}}}),V=a(1241);const T=(0,V.A)(P,[["render",y],["__scopeId","data-v-99c8b0fe"]]);var R=T}}]);
//# sourceMappingURL=product-tool.63bda1ab.js.map