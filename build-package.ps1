﻿# 读取AboutView.vue文件中的版本号
$aboutViewPath = ".\Alsi.Tab.Kit.Web\web\src\views\AboutView.vue"
$aboutViewContent = Get-Content $aboutViewPath -Raw -Encoding UTF8
$versionPattern = '版本\s*(\d+\.\d+\.\d+)'
$versionMatch = [regex]::Match($aboutViewContent, $versionPattern)

if (-not $versionMatch.Success) {
    Write-Error "无法在AboutView.vue文件中找到版本号！"
    exit 1
}

$currentVersion = $versionMatch.Groups[1].Value
Write-Host "当前版本号: $currentVersion"

# 询问用户是否要增加版本号
$increaseVersion = Read-Host "是否要增加版本号？(Y/N)"
if ($increaseVersion -eq "Y" -or $increaseVersion -eq "y") {
    # 解析版本号并增加小版本号
    $versionParts = $currentVersion.Split('.')
    $majorVersion = [int]$versionParts[0]
    $minorVersion = [int]$versionParts[1]
    $patchVersion = [int]$versionParts[2] + 1
    
    $newVersion = "$majorVersion.$minorVersion.$patchVersion"
    Write-Host "新版本号: $newVersion"
    
    # 更新AboutView.vue文件中的版本号
    $newContent = $aboutViewContent -replace $versionPattern, "版本 $newVersion"
    Set-Content -Path $aboutViewPath -Value $newContent -Encoding UTF8
    Write-Host "版本号已更新到 $newVersion"
    
    $finalVersion = $newVersion
} else {
    $finalVersion = $currentVersion
}

# 使用版本号作为zip文件名
$zipFileName = "TabKit_v$finalVersion.zip"
$sourceDirectory = ".\bin\Debug\x64"
$targetZipPath = ".\$zipFileName"

# 清空编译
msbuild -p:configuration=Debug -p:platform=x64 /t:Clean -verbosity:minimal;

# 编译
msbuild .\Alsi.Tab.Kit.sln -p:configuration=Debug -p:platform=x64 -verbosity:minimal;

# 检查x64文件夹是否存在
if (-not (Test-Path $sourceDirectory)) {
    Write-Error "x64文件夹不存在！"
    exit 1
}

# 删除指定类型的文件
Write-Host "正在删除不需要的文件..."
Get-ChildItem -Path $sourceDirectory -File | Where-Object {
    $_.Name -like "*.dll.config" -or $_.Extension -in @(".pdb", ".xml")
} | Remove-Item -Force

# 删除locales下的pak文件（排除en-US和zh-CN）
$localesPath = Join-Path $sourceDirectory "CefSharp\locales"
if (Test-Path $localesPath) {
    Get-ChildItem -Path $localesPath -Filter "*.pak" | Where-Object {
        $_.Name -notin @("en-US.pak", "zh-CN.pak")
    } | Remove-Item -Force
}

# 创建zip文件
Write-Host "正在创建压缩文件..."
if (Test-Path $targetZipPath) {
    Remove-Item $targetZipPath -Force
}
Compress-Archive -Path "$sourceDirectory\*" -DestinationPath $targetZipPath

# 获取文件大小
$fileSize = (Get-Item $targetZipPath).Length
$fileSizeMB = [math]::Round($fileSize / 1MB, 2)

# 显示信息并询问用户
Write-Host "`n生成的文件信息："
Write-Host "文件名: $zipFileName"
Write-Host "文件大小: $fileSizeMB MB"

Write-Host "`n脚本执行完成"
