import { createRouter, createWebHistory, RouteRecordRaw } from "vue-router";
import HomeView from "../views/HomeView.vue";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    name: "home",
    component: HomeView,
  },
  {
    path: "/log-converter",
    name: "log-converter",
    component: () =>
      import(/* webpackChunkName: "log-converter" */ "../views/LogConverterView.vue"),
  },
  {
    path: "/parameter-tool",
    name: "parameter-tool",
    component: () =>
      import(/* webpackChunkName: "parameter-tool" */ "../views/ParameterToolView.vue"),
  },
  {
    path: "/product-tool",
    name: "product-tool",
    component: () =>
      import(/* webpackChunkName: "product-tool" */ "../views/ProductToolView.vue"),
  },
  {
    path: "/about",
    name: "about",
    component: () =>
      import(/* webpackChunkName: "about" */ "../views/AboutView.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
});

export default router;
